<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF <PERSON> -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', config('app.name', 'Laravel'))</title>

    <!-- Fonts -->
    <link rel="dns-prefetch" href="//fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=Nunito:300,400,600,700,800" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <!-- Scripts -->
    @vite(['resources/sass/app.scss', 'resources/js/app.js'])

    <!-- Custom Styles -->
    @stack('styles')
</head>
<body>
    <div id="app">
        <nav class="modern-navbar">
            <div class="container">
                <div class="navbar-content">
                    <a class="navbar-brand" href="{{ url('/') }}">
                        <div class="brand-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="brand-text">
                            <span class="brand-name">{{ config('app.name', 'IELTS AI') }}</span>
                            <span class="brand-subtitle">Scoring System</span>
                        </div>
                    </a>

                    <button class="mobile-toggle" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>

                    <div class="collapse navbar-collapse" id="navbarSupportedContent">
                        <div class="navbar-nav-container">
                            <!-- Navigation Menu -->
                            <div class="navbar-menu">
                                @auth
                                    <div class="nav-links">
                                        <a class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}" href="{{ route('dashboard') }}">
                                            <i class="fas fa-chart-line"></i>
                                            <span>Dashboard</span>
                                        </a>
                                        <a class="nav-link {{ request()->routeIs('scoring.create') ? 'active' : '' }}" href="{{ route('scoring.create') }}">
                                            <i class="fas fa-edit"></i>
                                            <span>Chấm thi</span>
                                        </a>
                                        <a class="nav-link {{ request()->routeIs('scoring.history') ? 'active' : '' }}" href="{{ route('scoring.history') }}">
                                            <i class="fas fa-history"></i>
                                            <span>Lịch sử</span>
                                        </a>
                                        @if(auth()->user()->isAdmin())
                                            <a class="nav-link admin-link {{ request()->routeIs('admin.*') ? 'active' : '' }}" href="{{ route('admin.index') }}">
                                                <i class="fas fa-crown"></i>
                                                <span>Admin</span>
                                            </a>
                                        @endif
                                    </div>
                                @else
                                    <div class="nav-links">
                                        <a class="nav-link" href="{{ url('/') }}">
                                            <i class="fas fa-home"></i>
                                            <span>Trang chủ</span>
                                        </a>
                                    </div>
                                @endauth
                            </div>

                        <!-- User Menu -->
                        <div class="navbar-user">
                            @guest
                                <div class="auth-links">
                                    @if (Route::has('login'))
                                        <a class="auth-link login-link" href="{{ route('login') }}">
                                            <i class="fas fa-sign-in-alt"></i>
                                            <span>Đăng nhập</span>
                                        </a>
                                    @endif

                                    @if (Route::has('register'))
                                        <a class="auth-link register-link" href="{{ route('register') }}">
                                            <i class="fas fa-user-plus"></i>
                                            <span>Đăng ký</span>
                                        </a>
                                    @endif
                                </div>
                            @else
                                <div class="dropdown">
                                    <button class="user-button dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false" onclick="toggleUserDropdown()">
                                        <div class="user-avatar">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div class="user-info">
                                            <span class="user-name">{{ Auth::user()->name }}</span>
                                            @if(Auth::user()->credit)
                                                <span class="user-credits">{{ Auth::user()->getAvailableCredits() }} credits</span>
                                            @endif
                                        </div>
                                        <i class="fas fa-chevron-down dropdown-arrow"></i>
                                    </button>

                                    <ul class="dropdown-menu dropdown-menu-end modern-dropdown" aria-labelledby="userDropdown">
                                        <li class="dropdown-header">
                                            <div class="user-avatar-large">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <div class="user-details">
                                                <div class="user-name">{{ Auth::user()->name }}</div>
                                                <div class="user-email">{{ Auth::user()->email }}</div>
                                                @if(Auth::user()->credit)
                                                    <div class="user-credits-info">
                                                        <i class="fas fa-coins"></i>
                                                        {{ Auth::user()->getAvailableCredits() }} credits còn lại
                                                    </div>
                                                @endif
                                            </div>
                                        </li>

                                        <li><hr class="dropdown-divider"></li>

                                        <li><a class="dropdown-item" href="{{ route('dashboard') }}">
                                            <i class="fas fa-chart-line"></i>
                                            <span>Dashboard</span>
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ route('scoring.history') }}">
                                            <i class="fas fa-history"></i>
                                            <span>Lịch sử chấm thi</span>
                                        </a></li>

                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="{{ route('profile.edit') }}">
                                            <i class="fas fa-edit"></i>
                                            <span>Chỉnh sửa thông tin</span>
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ route('profile.change-password') }}">
                                            <i class="fas fa-key"></i>
                                            <span>Đổi mật khẩu</span>
                                        </a></li>

                                        <li><hr class="dropdown-divider"></li>

                                        <li><a class="dropdown-item logout-item" href="{{ route('logout') }}"
                                           onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                            <i class="fas fa-sign-out-alt"></i>
                                            <span>Đăng xuất</span>
                                        </a></li>

                                        <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                                            @csrf
                                        </form>
                                    </ul>
                                </div>
                            @endguest
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <main class="py-0">
            @yield('content')
        </main>

        <!-- Footer -->
        <footer class="bg-dark text-white py-4 mt-5">
            <div class="container">
                <div class="row">
                    <div class="col-md-6">
                        <h5><i class="fas fa-robot me-2"></i>{{ config('app.name') }}</h5>
                        <p class="text-muted">Hệ thống chấm thi IELTS Writing với AI tiên tiến</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <p class="text-muted mb-0">&copy; {{ date('Y') }} {{ config('app.name') }}. All rights reserved.</p>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    @stack('scripts')

    <script>
    // Mobile menu toggle
    document.addEventListener('DOMContentLoaded', function() {
        const mobileToggle = document.querySelector('.mobile-toggle');
        const navbarCollapse = document.querySelector('#navbarSupportedContent');

        if (mobileToggle && navbarCollapse) {
            mobileToggle.addEventListener('click', function() {
                navbarCollapse.classList.toggle('show');
            });
        }

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!mobileToggle.contains(e.target) && !navbarCollapse.contains(e.target)) {
                navbarCollapse.classList.remove('show');
            }
        });

        // Initialize Bootstrap dropdowns
        var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
        var dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
            return new bootstrap.Dropdown(dropdownToggleEl);
        });

        // User dropdown arrow animation
        const userDropdown = document.getElementById('userDropdown');
        if (userDropdown) {
            userDropdown.addEventListener('show.bs.dropdown', function () {
                const arrow = this.querySelector('.dropdown-arrow');
                if (arrow) arrow.style.transform = 'rotate(180deg)';
            });

            userDropdown.addEventListener('hide.bs.dropdown', function () {
                const arrow = this.querySelector('.dropdown-arrow');
                if (arrow) arrow.style.transform = 'rotate(0deg)';
            });
        }
    });

    // Manual dropdown toggle function
    function toggleUserDropdown() {
        const dropdown = document.getElementById('userDropdown');
        const dropdownMenu = dropdown.nextElementSibling;
        const arrow = dropdown.querySelector('.dropdown-arrow');

        if (dropdownMenu.classList.contains('show')) {
            dropdownMenu.classList.remove('show');
            dropdown.setAttribute('aria-expanded', 'false');
            if (arrow) arrow.style.transform = 'rotate(0deg)';
        } else {
            dropdownMenu.classList.add('show');
            dropdown.setAttribute('aria-expanded', 'true');
            if (arrow) arrow.style.transform = 'rotate(180deg)';
        }
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
        const dropdown = document.getElementById('userDropdown');
        const dropdownMenu = dropdown?.nextElementSibling;

        if (dropdown && dropdownMenu && !dropdown.contains(event.target) && !dropdownMenu.contains(event.target)) {
            dropdownMenu.classList.remove('show');
            dropdown.setAttribute('aria-expanded', 'false');
            const arrow = dropdown.querySelector('.dropdown-arrow');
            if (arrow) arrow.style.transform = 'rotate(0deg)';
        }
    });
    </script>

    <style>
    /* Modern Navbar Styles */
    .modern-navbar {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        backdrop-filter: blur(20px);
        border-bottom: 1px solid rgba(255,255,255,0.1);
        padding: 1rem 0;
        position: sticky;
        top: 0;
        z-index: 1000;
        box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    }

    .navbar-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        position: relative;
    }

    .navbar-nav-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        flex: 1;
    }

    /* Brand Styles */
    .navbar-brand {
        display: flex;
        align-items: center;
        gap: 1rem;
        text-decoration: none;
        color: white;
        transition: all 0.3s ease;
    }

    .navbar-brand:hover {
        color: white;
        transform: translateY(-2px);
    }

    .brand-icon {
        width: 50px;
        height: 50px;
        background: rgba(255,255,255,0.2);
        backdrop-filter: blur(20px);
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        border: 1px solid rgba(255,255,255,0.3);
    }

    .brand-text {
        display: flex;
        flex-direction: column;
    }

    .brand-name {
        font-size: 1.5rem;
        font-weight: 800;
        line-height: 1;
    }

    .brand-subtitle {
        font-size: 0.8rem;
        opacity: 0.8;
        font-weight: 400;
    }

    /* Mobile Toggle */
    .mobile-toggle {
        display: none;
        flex-direction: column;
        gap: 4px;
        background: none;
        border: none;
        padding: 8px;
        cursor: pointer;
    }

    .mobile-toggle span {
        width: 25px;
        height: 3px;
        background: white;
        border-radius: 2px;
        transition: all 0.3s ease;
    }

    /* Navigation Menu */
    .navbar-menu {
        display: flex;
        justify-content: center;
        flex: 1;
    }

    .nav-links {
        display: flex;
        gap: 1.5rem;
        align-items: center;
        flex-wrap: wrap;
    }

    .nav-link {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        border-radius: 50px;
        text-decoration: none;
        color: rgba(255,255,255,0.9);
        font-weight: 500;
        transition: all 0.3s ease;
        position: relative;
    }

    .nav-link:hover {
        color: white;
        background: rgba(255,255,255,0.1);
        transform: translateY(-2px);
    }

    .nav-link.active {
        background: rgba(255,255,255,0.2);
        color: white;
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255,255,255,0.3);
    }

    .nav-link.admin-link {
        background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        color: white;
        border: 1px solid rgba(255,255,255,0.3);
    }

    .nav-link.admin-link:hover {
        background: linear-gradient(135deg, #ee5a24, #ff6b6b);
        transform: translateY(-2px) scale(1.05);
    }

    /* User Menu */
    .navbar-user {
        display: flex;
        align-items: center;
        margin-left: auto;
    }

    .auth-links {
        display: flex;
        gap: 1rem;
        align-items: center;
    }

    .auth-link {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        border-radius: 50px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .login-link {
        color: white;
        border: 2px solid rgba(255,255,255,0.3);
    }

    .login-link:hover {
        background: rgba(255,255,255,0.1);
        border-color: rgba(255,255,255,0.5);
        color: white;
        transform: translateY(-2px);
    }

    .register-link {
        background: rgba(255,255,255,0.2);
        color: white;
        border: 1px solid rgba(255,255,255,0.3);
    }

    .register-link:hover {
        background: rgba(255,255,255,0.3);
        color: white;
        transform: translateY(-2px);
    }

    /* User Dropdown */
    .dropdown {
        position: relative;
    }

    .user-button {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 0.75rem 1.5rem;
        background: rgba(255,255,255,0.1);
        border: 1px solid rgba(255,255,255,0.2);
        border-radius: 50px;
        color: white;
        cursor: pointer;
        transition: all 0.3s ease;
        backdrop-filter: blur(20px);
    }

    .user-button:hover {
        background: rgba(255,255,255,0.2);
        transform: translateY(-2px);
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        background: rgba(255,255,255,0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
    }

    .user-info {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }

    .user-name {
        font-weight: 600;
        font-size: 0.95rem;
        line-height: 1;
    }

    .user-credits {
        font-size: 0.75rem;
        opacity: 0.8;
        background: rgba(255,255,255,0.2);
        padding: 0.2rem 0.5rem;
        border-radius: 10px;
        margin-top: 0.2rem;
    }

    .dropdown-arrow {
        font-size: 0.8rem;
        transition: transform 0.3s ease;
    }

    .dropdown.show .dropdown-arrow {
        transform: rotate(180deg);
    }

    .user-button.dropdown-toggle::after {
        display: none; /* Hide default Bootstrap arrow */
    }

    /* Modern Dropdown */
    .modern-dropdown {
        background: white !important;
        border: none !important;
        border-radius: 20px !important;
        box-shadow: 0 20px 60px rgba(0,0,0,0.15) !important;
        padding: 0 !important;
        margin-top: 1rem !important;
        min-width: 300px !important;
        overflow: hidden !important;
        position: absolute !important;
        z-index: 1050 !important;
    }

    .dropdown-header {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        padding: 1.5rem;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .user-avatar-large {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
    }

    .user-details {
        flex: 1;
    }

    .user-details .user-name {
        font-size: 1.1rem;
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 0.25rem;
    }

    .user-email {
        font-size: 0.9rem;
        color: #718096;
        margin-bottom: 0.5rem;
    }

    .user-credits-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.85rem;
        color: #667eea;
        font-weight: 600;
    }

    .dropdown-item {
        display: flex !important;
        align-items: center !important;
        gap: 1rem !important;
        padding: 1rem 1.5rem !important;
        color: #2d3748 !important;
        text-decoration: none !important;
        transition: all 0.3s ease !important;
        border: none !important;
        background: none !important;
        width: 100% !important;
        text-align: left !important;
        font-size: 0.95rem !important;
    }

    .dropdown-item:hover {
        background: #f8f9fa !important;
        color: #667eea !important;
        transform: translateX(5px) !important;
        text-decoration: none !important;
    }

    .dropdown-item i {
        width: 20px;
        text-align: center;
        font-size: 1rem;
    }

    .logout-item {
        color: #e53e3e;
    }

    .logout-item:hover {
        background: #fed7d7;
        color: #c53030;
    }

    .dropdown-divider {
        margin: 0;
        border-color: #e2e8f0;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .mobile-toggle {
            display: flex;
        }

        .navbar-collapse {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: rgba(102, 126, 234, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 0 0 20px 20px;
            padding: 2rem;
            margin-top: 1rem;
            z-index: 1000;
        }

        .navbar-nav-container {
            flex-direction: column;
            gap: 2rem;
        }

        .navbar-menu {
            justify-content: flex-start;
            width: 100%;
        }

        .nav-links {
            flex-direction: column;
            gap: 1rem;
            width: 100%;
        }

        .nav-link {
            width: 100%;
            justify-content: flex-start;
            padding: 1rem;
        }

        .auth-links {
            flex-direction: column;
            gap: 1rem;
            width: 100%;
        }

        .auth-link {
            width: 100%;
            justify-content: center;
        }

        .user-button {
            width: 100%;
            justify-content: flex-start;
        }

        .brand-text {
            display: none;
        }

        .navbar-user {
            width: 100%;
            margin-left: 0;
        }
    }

    /* Ensure navbar is always visible on desktop */
    @media (min-width: 769px) {
        .navbar-collapse {
            display: flex !important;
        }

        .mobile-toggle {
            display: none !important;
        }
    }

    /* Mobile navbar */
    @media (max-width: 768px) {
        .navbar-collapse {
            display: none;
        }

        .navbar-collapse.show {
            display: block !important;
        }
    }
    </style>
</body>
</html>
