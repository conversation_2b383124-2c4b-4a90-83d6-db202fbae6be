@extends('layouts.admin')

@section('title', 'Cài Đặt Điều Chỉnh Điểm Số')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-cogs"></i>
                        Cài Đặt Điều Chỉnh Điểm Số
                    </h3>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <!-- Quick Uniform Adjustment -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-magic"></i>
                                        Điều Chỉnh Đồng Loạt
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <form action="{{ route('admin.settings.uniform-adjustment') }}" method="POST">
                                        @csrf
                                        <div class="mb-3">
                                            <label for="uniform_adjustment" class="form-label">
                                                Giảm tất cả các chỉ số (điểm):
                                            </label>
                                            <div class="input-group">
                                                <input type="number" 
                                                       class="form-control" 
                                                       id="uniform_adjustment" 
                                                       name="uniform_adjustment" 
                                                       value="0.5" 
                                                       min="0" 
                                                       max="9" 
                                                       step="0.1" 
                                                       required>
                                                <button class="btn btn-primary" type="submit">
                                                    <i class="fas fa-check"></i>
                                                    Áp Dụng
                                                </button>
                                            </div>
                                            <small class="form-text text-muted">
                                                Áp dụng cùng một mức điều chỉnh cho tất cả các tiêu chí
                                            </small>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-dark">
                                    <h5 class="mb-0">
                                        <i class="fas fa-undo"></i>
                                        Khôi Phục Mặc Định
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <p class="mb-3">Khôi phục tất cả cài đặt về giá trị mặc định (giảm 0.5 điểm)</p>
                                    <form action="{{ route('admin.settings.reset-defaults') }}" method="POST" 
                                          onsubmit="return confirm('Bạn có chắc chắn muốn khôi phục về cài đặt mặc định?')">
                                        @csrf
                                        <button type="submit" class="btn btn-warning">
                                            <i class="fas fa-undo"></i>
                                            Khôi Phục Mặc Định
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Detailed Settings -->
                    <form action="{{ route('admin.settings.score-adjustments') }}" method="POST">
                        @csrf
                        
                        <div class="row">
                            <!-- Score Adjustments -->
                            <div class="col-md-8">
                                <h4 class="mb-3">
                                    <i class="fas fa-sliders-h"></i>
                                    Điều Chỉnh Chi Tiết Theo Tiêu Chí
                                </h4>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="overall_band_score" class="form-label">
                                            Overall Band Score (điểm tổng):
                                        </label>
                                        <input type="number" 
                                               class="form-control" 
                                               id="overall_band_score" 
                                               name="overall_band_score" 
                                               value="{{ $settings['overall_band_score'] }}" 
                                               min="0" 
                                               max="9" 
                                               step="0.1" 
                                               required>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="task_achievement" class="form-label">
                                            Task Achievement:
                                        </label>
                                        <input type="number" 
                                               class="form-control" 
                                               id="task_achievement" 
                                               name="task_achievement" 
                                               value="{{ $settings['task_achievement'] }}" 
                                               min="0" 
                                               max="9" 
                                               step="0.1" 
                                               required>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="coherence_cohesion" class="form-label">
                                            Coherence & Cohesion:
                                        </label>
                                        <input type="number" 
                                               class="form-control" 
                                               id="coherence_cohesion" 
                                               name="coherence_cohesion" 
                                               value="{{ $settings['coherence_cohesion'] }}" 
                                               min="0" 
                                               max="9" 
                                               step="0.1" 
                                               required>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="lexical_resource" class="form-label">
                                            Lexical Resource:
                                        </label>
                                        <input type="number" 
                                               class="form-control" 
                                               id="lexical_resource" 
                                               name="lexical_resource" 
                                               value="{{ $settings['lexical_resource'] }}" 
                                               min="0" 
                                               max="9" 
                                               step="0.1" 
                                               required>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="grammar_accuracy" class="form-label">
                                            Grammar & Accuracy:
                                        </label>
                                        <input type="number" 
                                               class="form-control" 
                                               id="grammar_accuracy" 
                                               name="grammar_accuracy" 
                                               value="{{ $settings['grammar_accuracy'] }}" 
                                               min="0" 
                                               max="9" 
                                               step="0.1" 
                                               required>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- General Settings -->
                            <div class="col-md-4">
                                <h4 class="mb-3">
                                    <i class="fas fa-cog"></i>
                                    Cài Đặt Chung
                                </h4>
                                
                                <div class="mb-3">
                                    <label for="minimum_score" class="form-label">Điểm tối thiểu:</label>
                                    <input type="number" 
                                           class="form-control" 
                                           id="minimum_score" 
                                           name="minimum_score" 
                                           value="{{ $settings['minimum_score'] }}" 
                                           min="0" 
                                           max="9" 
                                           step="0.1" 
                                           required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="maximum_score" class="form-label">Điểm tối đa:</label>
                                    <input type="number" 
                                           class="form-control" 
                                           id="maximum_score" 
                                           name="maximum_score" 
                                           value="{{ $settings['maximum_score'] }}" 
                                           min="0" 
                                           max="9" 
                                           step="0.1" 
                                           required>
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="enable_rounding" 
                                           name="enable_rounding" 
                                           {{ $settings['enable_rounding'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="enable_rounding">
                                        Làm tròn theo chuẩn IELTS (0.5)
                                    </label>
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="show_original_scores" 
                                           name="show_original_scores" 
                                           {{ $settings['show_original_scores'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="show_original_scores">
                                        Hiển thị điểm gốc
                                    </label>
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="show_adjustment_info" 
                                           name="show_adjustment_info" 
                                           {{ $settings['show_adjustment_info'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="show_adjustment_info">
                                        Hiển thị thông tin điều chỉnh
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-12">
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-save"></i>
                                    Lưu Cài Đặt
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn {
    border-radius: 0.375rem;
}

.alert {
    border-radius: 0.375rem;
}
</style>
@endsection
