@extends('layouts.admin')

@section('title', 'Chi Tiết Bài Thi - Admin')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.index') }}">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.scoring-attempts.index') }}">Quản lý bài thi</a>
                            </li>
                            <li class="breadcrumb-item active">Chi tiết bài thi #{{ $attempt->id }}</li>
                        </ol>
                    </nav>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-file-alt text-primary"></i>
                        Chi Tiết Bài Thi #{{ $attempt->id }}
                    </h1>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('admin.scoring-attempts.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i>
                        Quay lại
                    </a>
                    @if($attempt->canBeShared())
                        <a href="{{ $attempt->getShareUrl() }}" target="_blank" class="btn btn-info">
                            <i class="fas fa-external-link-alt"></i>
                            Xem công khai
                        </a>
                    @endif
                    <button type="button" class="btn btn-danger" onclick="deleteAttempt({{ $attempt->id }})">
                        <i class="fas fa-trash"></i>
                        Xóa bài thi
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- User Information -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Thông tin người dùng</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="avatar-lg me-3">
                            <div class="avatar-circle bg-primary text-white">
                                {{ substr($attempt->user->name, 0, 2) }}
                            </div>
                        </div>
                        <div>
                            <h5 class="mb-1">{{ $attempt->user->name }}</h5>
                            <p class="text-muted mb-0">{{ $attempt->user->email }}</p>
                        </div>
                    </div>

                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <div class="h5 mb-0 text-primary">{{ $attempt->user->role }}</div>
                                <div class="small text-muted">Vai trò</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="h5 mb-0 text-success">{{ $attempt->user->getAvailableCredits() }}</div>
                            <div class="small text-muted">Credits còn lại</div>
                        </div>
                    </div>

                    <hr>

                    <div class="small">
                        <div class="row mb-2">
                            <div class="col-sm-5 font-weight-bold">Tham gia:</div>
                            <div class="col-sm-7">{{ $attempt->user->created_at->format('d/m/Y') }}</div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-sm-5 font-weight-bold">Tổng bài thi:</div>
                            <div class="col-sm-7">{{ $attempt->user->scoringAttempts()->count() }}</div>
                        </div>
                        <div class="row">
                            <div class="col-sm-5 font-weight-bold">Bài hoàn thành:</div>
                            <div class="col-sm-7">{{ $attempt->user->scoringAttempts()->where('status', 'completed')->count() }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Attempt Details -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Thông tin bài thi</h6>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>Loại bài thi:</strong>
                            <span class="badge bg-info ms-2">
                                {{ ucfirst(str_replace('_', ' ', $attempt->task_type)) }}
                            </span>
                        </div>
                        <div class="col-md-6">
                            <strong>Trạng thái:</strong>
                            @if($attempt->status === 'completed')
                                <span class="badge bg-success ms-2">
                                    <i class="fas fa-check"></i> Hoàn thành
                                </span>
                            @elseif($attempt->status === 'pending')
                                <span class="badge bg-warning ms-2">
                                    <i class="fas fa-clock"></i> Đang xử lý
                                </span>
                            @else
                                <span class="badge bg-danger ms-2">
                                    <i class="fas fa-times"></i> Lỗi
                                </span>
                            @endif
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>Ngày tạo:</strong>
                            <span class="ms-2">{{ $attempt->created_at->format('d/m/Y H:i') }}</span>
                        </div>
                        <div class="col-md-6">
                            <strong>Số từ:</strong>
                            <span class="badge bg-secondary ms-2">{{ $attempt->word_count ?? 0 }} từ</span>
                        </div>
                    </div>

                    @if($attempt->rescored_at)
                        <div class="row mb-3">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    <strong>Bài thi đã được chấm lại:</strong> {{ $attempt->rescored_at->format('d/m/Y H:i') }}
                                </div>
                            </div>
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-12">
                            <strong>Câu hỏi:</strong>
                            <div class="mt-2 p-3 bg-light rounded">
                                {{ $attempt->getQuestionText() ?? 'Không có câu hỏi' }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if($attempt->isCompleted())
        <!-- Scoring Results -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Kết quả chấm điểm</h6>
                    </div>
                    <div class="card-body">
                        <!-- Overall Score -->
                        <div class="row mb-4">
                            <div class="col-12 text-center">
                                <div class="overall-score-display">
                                    <div class="score-circle">
                                        <div class="score-number">{{ $attempt->overall_band_score }}</div>
                                        <div class="score-label">Overall Band Score</div>
                                    </div>
                                    <div class="score-description mt-2">
                                        <span class="badge bg-primary fs-6">{{ $attempt->getBandDescription() }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Criteria Breakdown -->
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <div class="criteria-card">
                                    <div class="criteria-icon bg-gradient-primary">
                                        <i class="fas fa-tasks"></i>
                                    </div>
                                    <div class="criteria-content">
                                        <div class="criteria-score">{{ $attempt->task_achievement ?? '-' }}</div>
                                        <div class="criteria-name">Task Achievement</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="criteria-card">
                                    <div class="criteria-icon bg-gradient-success">
                                        <i class="fas fa-link"></i>
                                    </div>
                                    <div class="criteria-content">
                                        <div class="criteria-score">{{ $attempt->coherence_cohesion ?? '-' }}</div>
                                        <div class="criteria-name">Coherence & Cohesion</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="criteria-card">
                                    <div class="criteria-icon bg-gradient-warning">
                                        <i class="fas fa-book"></i>
                                    </div>
                                    <div class="criteria-content">
                                        <div class="criteria-score">{{ $attempt->lexical_resource ?? '-' }}</div>
                                        <div class="criteria-name">Lexical Resource</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="criteria-card">
                                    <div class="criteria-icon bg-gradient-info">
                                        <i class="fas fa-spell-check"></i>
                                    </div>
                                    <div class="criteria-content">
                                        <div class="criteria-score">{{ $attempt->grammar_accuracy ?? '-' }}</div>
                                        <div class="criteria-name">Grammar & Accuracy</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Essay Content -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Nội dung bài viết</h6>
                </div>
                <div class="card-body">
                    <div class="essay-content">
                        {{ $attempt->essay_content }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if($attempt->detailed_feedback)
        <!-- Detailed Feedback -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Phản hồi chi tiết</h6>
                    </div>
                    <div class="card-body">
                        @foreach($attempt->detailed_feedback as $criterion => $feedback)
                            <div class="feedback-section mb-4">
                                <h6 class="text-primary">
                                    {{ ucfirst(str_replace('_', ' ', $criterion)) }}
                                </h6>
                                <div class="feedback-content p-3 bg-light rounded">
                                    @if(is_array($feedback))
                                        @foreach($feedback as $key => $value)
                                            <div class="mb-2">
                                                <strong>{{ ucfirst($key) }}:</strong>
                                                @if(is_array($value))
                                                    <ul class="mb-0 mt-1">
                                                        @foreach($value as $item)
                                                            <li>{{ $item }}</li>
                                                        @endforeach
                                                    </ul>
                                                @else
                                                    {{ $value }}
                                                @endif
                                            </div>
                                        @endforeach
                                    @else
                                        {{ $feedback }}
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Xác nhận xóa</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Bạn có chắc chắn muốn xóa bài thi này không?</p>
                <p class="text-danger"><strong>Lưu ý:</strong> Hành động này không thể hoàn tác!</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <form id="deleteForm" method="POST" action="{{ route('admin.scoring-attempts.destroy', $attempt->id) }}" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Xóa</button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 60px;
    height: 60px;
}

.avatar-circle {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 18px;
}

.card {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.overall-score-display {
    padding: 2rem 0;
}

.score-circle {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    color: white;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.score-number {
    font-size: 3rem;
    font-weight: 900;
    line-height: 1;
}

.score-label {
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    opacity: 0.9;
}

.criteria-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.criteria-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.criteria-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
    color: white;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.bg-gradient-info {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.criteria-score {
    font-size: 2rem;
    font-weight: 800;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.criteria-name {
    font-size: 0.9rem;
    font-weight: 600;
    color: #718096;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.essay-content {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 2rem;
    font-family: 'Georgia', serif;
    font-size: 1.1rem;
    line-height: 1.8;
    color: #2d3748;
    border-left: 4px solid #667eea;
}

.feedback-section {
    border-left: 4px solid #e2e8f0;
    padding-left: 1rem;
}

.feedback-content {
    font-size: 0.95rem;
    line-height: 1.6;
}

.breadcrumb {
    background: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: #6c757d;
}

.breadcrumb-item a {
    color: #667eea;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: #5a67d8;
    text-decoration: underline;
}

.border-end {
    border-right: 1px solid #e2e8f0 !important;
}

@media (max-width: 768px) {
    .score-circle {
        width: 120px;
        height: 120px;
    }

    .score-number {
        font-size: 2.5rem;
    }

    .criteria-card {
        margin-bottom: 1rem;
    }

    .essay-content {
        padding: 1.5rem;
        font-size: 1rem;
    }
}
</style>

<script>
function deleteAttempt(attemptId) {
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

// Add success message redirect after delete
document.getElementById('deleteForm')?.addEventListener('submit', function(e) {
    // Form will submit normally, redirect will be handled by controller
});
</script>
@endsection
