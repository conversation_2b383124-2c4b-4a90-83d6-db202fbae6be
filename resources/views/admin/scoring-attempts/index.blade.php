@extends('layouts.admin')

@section('title', 'Quản Lý Bài Thi - Admin')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-file-alt text-primary"></i>
                        Quản Lý Bài Thi
                    </h1>
                    <p class="text-muted mb-0">Xem và quản lý tất cả bài thi trong hệ thống</p>
                </div>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#filterModal">
                        <i class="fas fa-filter"></i>
                        <PERSON><PERSON> lọc
                    </button>
                    <a href="{{ route('admin.scoring-attempts.export', request()->query()) }}" class="btn btn-success">
                        <i class="fas fa-download"></i>
                        Xuất Excel
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Tổng số bài thi
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['total']) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Đã hoàn thành
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['completed']) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Đang xử lý
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['pending']) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Hôm nay
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['today']) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alerts -->
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Search and Filter Bar -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Tìm kiếm và Lọc</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.scoring-attempts.index') }}">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="search" class="form-label">Tìm kiếm người dùng</label>
                        <input type="text"
                               class="form-control"
                               id="search"
                               name="search"
                               value="{{ request('search') }}"
                               placeholder="Tên hoặc email...">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="status" class="form-label">Trạng thái</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">Tất cả</option>
                            @foreach($statuses as $status)
                                <option value="{{ $status }}" {{ request('status') == $status ? 'selected' : '' }}>
                                    {{ ucfirst($status) }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="task_type" class="form-label">Loại bài thi</label>
                        <select class="form-select" id="task_type" name="task_type">
                            <option value="">Tất cả</option>
                            @foreach($taskTypes as $type)
                                <option value="{{ $type }}" {{ request('task_type') == $type ? 'selected' : '' }}>
                                    {{ ucfirst(str_replace('_', ' ', $type)) }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="date_from" class="form-label">Từ ngày</label>
                        <input type="date"
                               class="form-control"
                               id="date_from"
                               name="date_from"
                               value="{{ request('date_from') }}">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="date_to" class="form-label">Đến ngày</label>
                        <input type="date"
                               class="form-control"
                               id="date_to"
                               name="date_to"
                               value="{{ request('date_to') }}">
                    </div>
                    <div class="col-md-1 mb-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                @if(request()->hasAny(['search', 'status', 'task_type', 'date_from', 'date_to']))
                    <div class="row">
                        <div class="col-12">
                            <a href="{{ route('admin.scoring-attempts.index') }}" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-times"></i>
                                Xóa bộ lọc
                            </a>
                        </div>
                    </div>
                @endif
            </form>
        </div>
    </div>

    <!-- Data Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                Danh sách bài thi ({{ $attempts->total() }} kết quả)
            </h6>
            @if($attempts->count() > 0)
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-danger btn-sm" onclick="bulkDelete()" id="bulkDeleteBtn" style="display: none;">
                        <i class="fas fa-trash"></i>
                        Xóa đã chọn
                    </button>
                </div>
            @endif
        </div>
        <div class="card-body">
            @if($attempts->count() > 0)
                <div class="table-responsive">
                    <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th width="30">
                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                </th>
                                <th>Người dùng</th>
                                <th>Loại bài thi</th>
                                <th>Câu hỏi</th>
                                <th>Điểm số</th>
                                <th>Số từ</th>
                                <th>Trạng thái</th>
                                <th>Ngày tạo</th>
                                <th width="120">Hành động</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($attempts as $attempt)
                                <tr>
                                    <td>
                                        <input type="checkbox" name="attempt_ids[]" value="{{ $attempt->id }}" class="form-check-input attempt-checkbox">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm me-2">
                                                <div class="avatar-circle bg-primary text-white">
                                                    {{ substr($attempt->user->name, 0, 1) }}
                                                </div>
                                            </div>
                                            <div>
                                                <div class="fw-bold">{{ $attempt->user->name }}</div>
                                                <div class="text-muted small">{{ $attempt->user->email }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            {{ ucfirst(str_replace('_', ' ', $attempt->task_type)) }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="text-truncate" style="max-width: 200px;" title="{{ $attempt->getQuestionText() }}">
                                            {{ $attempt->essayQuestion ? $attempt->essayQuestion->title : 'Custom Question' }}
                                        </div>
                                    </td>
                                    <td>
                                        @if($attempt->isCompleted())
                                            <div class="text-center">
                                                <div class="fw-bold text-primary">{{ $attempt->overall_band_score }}</div>
                                                <div class="small text-muted">Band Score</div>
                                            </div>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ $attempt->word_count ?? 0 }} từ</span>
                                    </td>
                                    <td>
                                        @if($attempt->status === 'completed')
                                            <span class="badge bg-success">
                                                <i class="fas fa-check"></i> Hoàn thành
                                            </span>
                                        @elseif($attempt->status === 'pending')
                                            <span class="badge bg-warning">
                                                <i class="fas fa-clock"></i> Đang xử lý
                                            </span>
                                        @else
                                            <span class="badge bg-danger">
                                                <i class="fas fa-times"></i> Lỗi
                                            </span>
                                        @endif
                                    </td>
                                    <td>
                                        <div>{{ $attempt->created_at->format('d/m/Y') }}</div>
                                        <div class="small text-muted">{{ $attempt->created_at->format('H:i') }}</div>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.scoring-attempts.show', $attempt->id) }}"
                                               class="btn btn-sm btn-outline-primary"
                                               title="Xem chi tiết">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button type="button"
                                                    class="btn btn-sm btn-outline-danger"
                                                    onclick="deleteAttempt({{ $attempt->id }})"
                                                    title="Xóa">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="text-muted">
                        Hiển thị {{ $attempts->firstItem() }} - {{ $attempts->lastItem() }}
                        trong tổng số {{ $attempts->total() }} kết quả
                    </div>
                    {{ $attempts->appends(request()->query())->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Không có bài thi nào</h5>
                    <p class="text-muted">Chưa có bài thi nào trong hệ thống hoặc không khớp với bộ lọc.</p>
                </div>
            @endif
        </div>
    </div>
</div>
<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Xác nhận xóa</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Bạn có chắc chắn muốn xóa bài thi này không?</p>
                <p class="text-danger"><strong>Lưu ý:</strong> Hành động này không thể hoàn tác!</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Xóa</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Delete Form -->
<form id="bulkDeleteForm" method="POST" action="{{ route('admin.scoring-attempts.bulk-delete') }}" style="display: none;">
    @csrf
    <div id="bulkDeleteIds"></div>
</form>

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
}

.avatar-circle {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}

.card {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.table th {
    background-color: #f8f9fc;
    border-color: #e3e6f0;
    font-weight: 600;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table td {
    border-color: #e3e6f0;
    vertical-align: middle;
}

.btn-group .btn {
    border-radius: 0.35rem;
}

.btn-group .btn:not(:last-child) {
    margin-right: 0.25rem;
}

.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.form-check-input:checked {
    background-color: #4e73df;
    border-color: #4e73df;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select All functionality
    const selectAllCheckbox = document.getElementById('selectAll');
    const attemptCheckboxes = document.querySelectorAll('.attempt-checkbox');
    const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');

    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            attemptCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            toggleBulkDeleteButton();
        });
    }

    attemptCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const checkedCount = document.querySelectorAll('.attempt-checkbox:checked').length;
            selectAllCheckbox.checked = checkedCount === attemptCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < attemptCheckboxes.length;
            toggleBulkDeleteButton();
        });
    });

    function toggleBulkDeleteButton() {
        const checkedCount = document.querySelectorAll('.attempt-checkbox:checked').length;
        if (bulkDeleteBtn) {
            bulkDeleteBtn.style.display = checkedCount > 0 ? 'inline-block' : 'none';
        }
    }
});

function deleteAttempt(attemptId) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `/admin/scoring-attempts/${attemptId}`;

    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

function bulkDelete() {
    const checkedBoxes = document.querySelectorAll('.attempt-checkbox:checked');

    if (checkedBoxes.length === 0) {
        alert('Vui lòng chọn ít nhất một bài thi để xóa.');
        return;
    }

    if (confirm(`Bạn có chắc chắn muốn xóa ${checkedBoxes.length} bài thi đã chọn không?\n\nHành động này không thể hoàn tác!`)) {
        const bulkDeleteForm = document.getElementById('bulkDeleteForm');
        const bulkDeleteIds = document.getElementById('bulkDeleteIds');

        // Clear existing inputs
        bulkDeleteIds.innerHTML = '';

        // Add selected IDs
        checkedBoxes.forEach(checkbox => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'attempt_ids[]';
            input.value = checkbox.value;
            bulkDeleteIds.appendChild(input);
        });

        bulkDeleteForm.submit();
    }
}

// Auto-submit form when date inputs change
document.getElementById('date_from')?.addEventListener('change', function() {
    if (this.value && document.getElementById('date_to').value) {
        this.form.submit();
    }
});

document.getElementById('date_to')?.addEventListener('change', function() {
    if (this.value && document.getElementById('date_from').value) {
        this.form.submit();
    }
});
</script>
@endsection
