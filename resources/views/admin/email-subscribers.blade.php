@extends('layouts.admin')

@section('title', 'Email Subscribers - Speaking AI')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-envelope"></i>
                        Email Subscribers - Speaking AI
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.email-subscribers.export') }}" class="btn btn-success btn-sm">
                            <i class="fas fa-download"></i>
                            Export CSV
                        </a>
                        <button class="btn btn-primary btn-sm" onclick="location.reload()">
                            <i class="fas fa-sync-alt"></i>
                            Refresh
                        </button>
                    </div>
                </div>
                
                <div class="card-body">
                    @if($subscribers->count() > 0)
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="stats-card bg-primary text-white">
                                    <div class="stats-content">
                                        <h3>{{ number_format($subscribers->count()) }}</h3>
                                        <p class="mb-0">Tổng số email đăng ký</p>
                                    </div>
                                    <div class="stats-icon">
                                        <i class="fas fa-users"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="stats-card bg-success text-white">
                                    <div class="stats-content">
                                        <h3>{{ $subscribers->where('timestamp', '>=', now()->subDays(7)->format('Y-m-d H:i:s'))->count() }}</h3>
                                        <p class="mb-0">Đăng ký trong 7 ngày qua</p>
                                    </div>
                                    <div class="stats-icon">
                                        <i class="fas fa-calendar-week"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>#</th>
                                        <th>Email</th>
                                        <th>Ngày đăng ký</th>
                                        <th>IP Address</th>
                                        <th>Trình duyệt</th>
                                        <th>Nguồn</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($subscribers as $index => $subscriber)
                                    <tr>
                                        <td>{{ $index + 1 }}</td>
                                        <td>
                                            <strong>{{ $subscriber['email'] ?? 'N/A' }}</strong>
                                        </td>
                                        <td>
                                            <span class="text-muted">
                                                {{ \Carbon\Carbon::parse($subscriber['timestamp'] ?? now())->format('d/m/Y H:i:s') }}
                                            </span>
                                            <br>
                                            <small class="text-success">
                                                {{ \Carbon\Carbon::parse($subscriber['timestamp'] ?? now())->diffForHumans() }}
                                            </small>
                                        </td>
                                        <td>
                                            <code>{{ $subscriber['ip'] ?? 'N/A' }}</code>
                                        </td>
                                        <td>
                                            <small class="text-muted" title="{{ $subscriber['user_agent'] ?? 'N/A' }}">
                                                {{ Str::limit($subscriber['user_agent'] ?? 'N/A', 50) }}
                                            </small>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">
                                                {{ $subscriber['source'] ?? 'speaking_ai_notification' }}
                                            </span>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <div class="empty-state">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <h4 class="text-muted">Chưa có email nào đăng ký</h4>
                                <p class="text-muted">Khi có người đăng ký nhận thông báo Speaking AI, danh sách sẽ hiển thị ở đây.</p>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.stats-card {
    border-radius: 10px;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.stats-content h3 {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stats-content p {
    font-size: 0.9rem;
    opacity: 0.9;
}

.stats-icon {
    font-size: 2.5rem;
    opacity: 0.7;
}

.empty-state {
    padding: 40px 20px;
}

.table th {
    border-top: none;
    font-weight: 600;
    font-size: 0.9rem;
}

.table td {
    vertical-align: middle;
}

.card-actions {
    display: flex;
    gap: 10px;
}

.badge {
    font-size: 0.75rem;
}

code {
    font-size: 0.8rem;
    padding: 2px 6px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}
</style>
@endpush
