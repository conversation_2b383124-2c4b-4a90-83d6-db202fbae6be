@extends('layouts.app')

@section('title', 'Đăng ký - IELTS AI Scoring System')

@section('content')
<div class="modern-auth-page">
    <div class="auth-container">
        <div class="auth-card">
            <!-- Left Side - Form -->
            <div class="auth-form-section">
                <div class="auth-header">
                    <h1 class="auth-title">Tạo tài khoản mới</h1>
                    <p class="auth-subtitle">Tham gia cộng đồng học IELTS với AI</p>
                </div>

                <form method="POST" action="{{ route('register') }}" class="auth-form">
                    @csrf

                    <!-- Name Field -->
                    <div class="form-group">
                        <label for="name" class="form-label">
                            <i class="fas fa-user"></i>
                            Họ và tên
                        </label>
                        <input id="name"
                               type="text"
                               class="form-input @error('name') error @enderror"
                               name="name"
                               value="{{ old('name') }}"
                               required
                               autocomplete="name"
                               autofocus
                               placeholder="Nhập họ và tên của bạn">
                        @error('name')
                            <span class="error-message">
                                <i class="fas fa-exclamation-circle"></i>
                                {{ $message }}
                            </span>
                        @enderror
                    </div>

                    <!-- Email Field -->
                    <div class="form-group">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope"></i>
                            Email
                        </label>
                        <input id="email"
                               type="email"
                               class="form-input @error('email') error @enderror"
                               name="email"
                               value="{{ old('email') }}"
                               required
                               autocomplete="email"
                               placeholder="Nhập địa chỉ email của bạn">
                        @error('email')
                            <span class="error-message">
                                <i class="fas fa-exclamation-circle"></i>
                                {{ $message }}
                            </span>
                        @enderror
                    </div>

                    <!-- Phone Field -->
                    <div class="form-group">
                        <label for="phone" class="form-label">
                            <i class="fas fa-phone"></i>
                            Số điện thoại
                        </label>
                        <input id="phone"
                               type="tel"
                               class="form-input @error('phone') error @enderror"
                               name="phone"
                               value="{{ old('phone') }}"
                               required
                               placeholder="Nhập số điện thoại (10-11 chữ số)"
                               pattern="[0-9]{10,11}">
                        <small class="form-help">
                            <i class="fas fa-info-circle"></i>
                            Số điện thoại để liên lạc hỗ trợ nạp lượt chấm thi
                        </small>
                        @error('phone')
                            <span class="error-message">
                                <i class="fas fa-exclamation-circle"></i>
                                {{ $message }}
                            </span>
                        @enderror
                    </div>

                    <!-- Password Field -->
                    <div class="form-group">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock"></i>
                            Mật khẩu
                        </label>
                        <div class="password-input-wrapper">
                            <input id="password"
                                   type="password"
                                   class="form-input @error('password') error @enderror"
                                   name="password"
                                   required
                                   autocomplete="new-password"
                                   placeholder="Nhập mật khẩu (tối thiểu 8 ký tự)">
                            <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        @error('password')
                            <span class="error-message">
                                <i class="fas fa-exclamation-circle"></i>
                                {{ $message }}
                            </span>
                        @enderror
                    </div>

                    <!-- Confirm Password Field -->
                    <div class="form-group">
                        <label for="password-confirm" class="form-label">
                            <i class="fas fa-lock"></i>
                            Xác nhận mật khẩu
                        </label>
                        <div class="password-input-wrapper">
                            <input id="password-confirm"
                                   type="password"
                                   class="form-input"
                                   name="password_confirmation"
                                   required
                                   autocomplete="new-password"
                                   placeholder="Nhập lại mật khẩu">
                            <button type="button" class="password-toggle" onclick="togglePassword('password-confirm')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <button type="submit" class="auth-submit-btn">
                        <i class="fas fa-user-plus"></i>
                        <span>Tạo tài khoản</span>
                        <div class="btn-loading">
                            <i class="fas fa-spinner fa-spin"></i>
                        </div>
                    </button>

                    <!-- Login Link -->
                    <div class="auth-footer">
                        <p>Đã có tài khoản?
                            <a href="{{ route('login') }}" class="auth-link">Đăng nhập ngay</a>
                        </p>
                    </div>
                </form>
            </div>

            <!-- Right Side - Illustration -->
            <div class="auth-illustration-section">
                <div class="illustration-content">

                    <div class="illustration-text">
                        <h2>Chào mừng đến với IELTS AI</h2>
                        <p>Hệ thống chấm thi IELTS Writing thông minh với công nghệ AI tiên tiến</p>
                        <div class="features-list">
                            <div class="feature-item">
                                <i class="fas fa-check-circle"></i>
                                <span>Chấm điểm chính xác với AI</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-check-circle"></i>
                                <span>Phản hồi chi tiết theo 4 tiêu chí</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-check-circle"></i>
                                <span>Theo dõi tiến độ học tập</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Modern Auth Page Styles */
.modern-auth-page {
    min-height: 100vh;

    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
}

.auth-container {
    width: 100%;
    max-width: 1200px;
}

.auth-card {
    background: white;
    border-radius: 24px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.15);
    overflow: hidden;
    display: grid;
    grid-template-columns: 1fr 1fr;
    min-height: 600px;
}

.auth-form-section {
    padding: 3rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-title {
    font-size: 2rem;
    font-weight: 800;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.auth-subtitle {
    color: #718096;
    font-size: 1rem;
    margin: 0;
}

.auth-form {
    width: 100%;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.form-input {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input.error {
    border-color: #e53e3e;
    background: #fed7d7;
}

.form-help {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #718096;
    font-size: 0.8rem;
    margin-top: 0.5rem;
}

.error-message {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #e53e3e;
    font-size: 0.85rem;
    margin-top: 0.5rem;
}

.password-input-wrapper {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #718096;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.password-toggle:hover {
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.auth-submit-btn {
    width: 100%;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1rem;
    position: relative;
    overflow: hidden;
}

.auth-submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.auth-submit-btn:active {
    transform: translateY(0);
}

.btn-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.auth-submit-btn.loading .btn-loading {
    opacity: 1;
}

.auth-submit-btn.loading span,
.auth-submit-btn.loading i:not(.btn-loading i) {
    opacity: 0;
}

.auth-footer {
    text-align: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e2e8f0;
}

.auth-footer p {
    color: #64748b;
    font-size: 0.95rem;
    margin: 0;
    font-weight: 500;
}

.auth-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 700;
    transition: all 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: inline-block;
    margin-left: 0.5rem;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.auth-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
    text-decoration: none;
}

/* Illustration Section */
.auth-illustration-section {
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.illustration-content {
    text-align: center;
    color: white;
    z-index: 2;
    position: relative;
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.floating-card {
    position: absolute;
    width: 80px;
    height: 80px;
    background: rgba(255,255,255,0.05);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: rgba(255,255,255,0.6);
    border: 1px solid rgba(255,255,255,0.1);
    z-index: 0; /* Behind text */
    opacity: 0.7;
}

.card-1 {
    top: 10%;
    left: 10%;
    animation: float1 6s ease-in-out infinite;
}

.card-2 {
    top: 15%;
    right: 10%;
    animation: float2 8s ease-in-out infinite;
}

.card-3 {
    bottom: 10%;
    left: 15%;
    animation: float3 7s ease-in-out infinite;
}

@keyframes float1 {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(10deg); }
}

@keyframes float2 {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-30px) rotate(-10deg); }
}

@keyframes float3 {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-25px) rotate(5deg); }
}

.illustration-text h2 {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
}

.illustration-text p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

.features-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
    text-align: left;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 1rem;
}

.feature-item i {
    color: #4ade80;
    font-size: 1.2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-card {
        grid-template-columns: 1fr;
        margin: 1rem;
    }

    .auth-illustration-section {
        display: none;
    }

    .auth-form-section {
        padding: 2rem;
    }

    .auth-title {
        font-size: 1.75rem;
    }
}
</style>

<script>
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const button = input.nextElementSibling;
    const icon = button.querySelector('i');

    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// Form submission loading state
document.querySelector('.auth-form').addEventListener('submit', function() {
    const submitBtn = document.querySelector('.auth-submit-btn');
    submitBtn.classList.add('loading');
    submitBtn.disabled = true;
});

// Phone number formatting
document.getElementById('phone').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    if (value.length > 11) {
        value = value.slice(0, 11);
    }
    e.target.value = value;
});
</script>
@endsection
