@extends('layouts.app')

@section('title', 'IELTS AI Scoring System')

@section('content')
<div class="container-fluid">
    <!-- Hero Section -->
    <div class="hero-section bg-gradient-primary text-white py-5 mb-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="display-4 fw-bold mb-4">IELTS AI Scoring System</h1>
                    <p class="lead mb-4">Hệ thống chấm thi IELTS Writing tự động với AI, giúp bạn cải thiện kỹ năng viết một cách hiệu quả và chính xác.</p>
                    <div class="d-flex gap-3">
                        @auth
                            <a href="{{ route('scoring.create') }}" class="btn btn-light btn-lg">
                                <i class="fas fa-edit me-2"></i>Bắt đầu chấm thi
                            </a>
                            <a href="{{ route('dashboard') }}" class="btn btn-outline-light btn-lg">
                                <i class="fas fa-chart-line me-2"></i>Dashboard
                            </a>
                        @else
                            <a href="{{ route('register') }}" class="btn btn-light btn-lg">
                                <i class="fas fa-user-plus me-2"></i>Đăng ký ngay
                            </a>
                            <a href="{{ route('login') }}" class="btn btn-outline-light btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>Đăng nhập
                            </a>
                        @endauth
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="text-center">
                        <i class="fas fa-robot fa-10x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Section -->
    <div class="container mb-5">
        <div class="row text-center mb-5">
            <div class="col-12">
                <h2 class="display-5 fw-bold mb-3">Tính năng nổi bật</h2>
                <p class="lead text-muted">Hệ thống chấm thi IELTS Writing với công nghệ AI tiên tiến</p>
            </div>
        </div>

        <div class="row g-4">
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon bg-primary text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                            <i class="fas fa-brain fa-2x"></i>
                        </div>
                        <h4 class="fw-bold mb-3">AI Chấm thi thông minh</h4>
                        <p class="text-muted">Sử dụng công nghệ AI tiên tiến để chấm thi chính xác theo tiêu chuẩn IELTS quốc tế</p>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon bg-success text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Phân tích chi tiết</h4>
                        <p class="text-muted">Báo cáo chi tiết về từng tiêu chí: Task Achievement, Coherence, Lexical Resource, Grammar</p>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon bg-warning text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                            <i class="fas fa-lightbulb fa-2x"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Gợi ý cải thiện</h4>
                        <p class="text-muted">Nhận được những gợi ý cụ thể để cải thiện kỹ năng viết và nâng cao band score</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sample Questions Section -->
    @if($questions->count() > 0)
    <div class="container mb-5">
        <div class="row">
            <div class="col-12">
                <h2 class="display-6 fw-bold mb-4">Câu hỏi mẫu</h2>
                <p class="lead text-muted mb-4">Thử sức với các câu hỏi IELTS Writing thực tế</p>
            </div>
        </div>

        <div class="row g-4">
            @foreach($questions->take(3) as $question)
            <div class="col-lg-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-header bg-light border-0">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="badge bg-primary">{{ $question->getTaskTypeLabel() }}</span>
                            <small class="text-muted">{{ $question->time_limit }} phút</small>
                        </div>
                    </div>
                    <div class="card-body">
                        <h5 class="card-title fw-bold">{{ $question->title }}</h5>
                        <p class="card-text text-muted">{{ Str::limit($question->question, 150) }}</p>
                        <div class="mt-auto">
                            <small class="text-muted">Tối thiểu {{ $question->min_words }} từ</small>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent border-0">
                        @auth
                            <a href="{{ route('scoring.create', ['question_id' => $question->id]) }}" class="btn btn-primary w-100">
                                <i class="fas fa-edit me-2"></i>Làm bài
                            </a>
                        @else
                            <a href="{{ route('login') }}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-sign-in-alt me-2"></i>Đăng nhập để làm bài
                            </a>
                        @endauth
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
    @endif

    <!-- Recent Attempts (for logged in users) -->
    @auth
    @if($recentAttempts->count() > 0)
    <div class="container mb-5">
        <div class="row">
            <div class="col-12">
                <h2 class="display-6 fw-bold mb-4">Bài thi gần đây</h2>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Câu hỏi</th>
                                        <th>Loại</th>
                                        <th>Band Score</th>
                                        <th>Trạng thái</th>
                                        <th>Ngày thi</th>
                                        <th>Hành động</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentAttempts as $attempt)
                                    <tr>
                                        <td>
                                            @if($attempt->essayQuestion)
                                                {{ Str::limit($attempt->essayQuestion->title, 50) }}
                                            @else
                                                <em>Câu hỏi tự do</em>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ ucfirst(str_replace('_', ' ', $attempt->task_type)) }}</span>
                                        </td>
                                        <td>
                                            @if($attempt->isCompleted())
                                                <span class="fw-bold text-primary">{{ $attempt->overall_band_score }}</span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($attempt->status === 'completed')
                                                <span class="badge bg-success">Hoàn thành</span>
                                            @elseif($attempt->status === 'pending')
                                                <span class="badge bg-warning">Đang xử lý</span>
                                            @else
                                                <span class="badge bg-danger">Lỗi</span>
                                            @endif
                                        </td>
                                        <td>{{ $attempt->created_at->format('d/m/Y H:i') }}</td>
                                        <td>
                                            @if($attempt->isCompleted())
                                                <a href="{{ route('scoring.show', $attempt->id) }}" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye me-1"></i>Xem
                                                </a>
                                            @endif
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
    @endauth

    <!-- CTA Section -->
    <div class="bg-light py-5">
        <div class="container text-center">
            <h2 class="display-6 fw-bold mb-3">Sẵn sàng cải thiện IELTS Writing?</h2>
            <p class="lead text-muted mb-4">Bắt đầu hành trình nâng cao band score của bạn ngay hôm nay</p>
            @auth
                <a href="{{ route('scoring.create') }}" class="btn btn-primary btn-lg">
                    <i class="fas fa-rocket me-2"></i>Bắt đầu ngay
                </a>
            @else
                <a href="{{ route('register') }}" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-user-plus me-2"></i>Đăng ký miễn phí
                </a>
                <a href="{{ route('login') }}" class="btn btn-outline-primary btn-lg">
                    <i class="fas fa-sign-in-alt me-2"></i>Đăng nhập
                </a>
            @endauth
        </div>
    </div>
</div>

<style>
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.feature-icon {
    transition: transform 0.3s ease;
}

.card:hover .feature-icon {
    transform: scale(1.1);
}

.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
}
</style>
@endsection
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.business-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
    opacity: 0.3;
}

.business-content {
    position: relative;
    z-index: 2;
}

.brand-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 600;
    margin-bottom: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.brand-badge i {
    color: #ffd700;
}

.business-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.business-title .highlight {
    color: #ffd700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.business-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.benefits-grid {
    display: grid;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.benefit-item {
    display: flex;
    gap: 1rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.benefit-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
}

.benefit-icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.benefit-content h5 {
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #ffd700;
}

.benefit-content p {
    margin: 0;
    opacity: 0.9;
    line-height: 1.5;
}

.business-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-business {
    padding: 0.875rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
    border: none;
}

.btn-primary-business {
    background: #ffd700;
    color: #333;
}

.btn-primary-business:hover {
    background: #ffed4e;
    color: #333;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
}

.btn-outline-business {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-outline-business:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

.business-visual {
    position: relative;
    z-index: 2;
}

.stats-showcase {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-item {
    background: rgba(255, 255, 255, 0.15);
    padding: 1.5rem;
    border-radius: 15px;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 800;
    color: #ffd700;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.features-showcase {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.feature-highlight {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem 1.5rem;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-highlight i {
    color: #ffd700;
    font-size: 1.2rem;
}

.feature-highlight span {
    font-weight: 600;
}

/* Responsive */
@media (max-width: 768px) {
    .business-title {
        font-size: 2rem;
    }

    .business-actions {
        flex-direction: column;
    }

    .btn-business {
        justify-content: center;
    }

    .stats-showcase {
        grid-template-columns: 1fr;
    }

    .benefit-item {
        flex-direction: column;
        text-align: center;
    }
}
</style>
@endsection
