<?php

echo "=== TESTING IMPROVED PROMPT STRUCTURE ===\n\n";

// Simulate the improved prompt structure
$testEssay = "
Nowadays, many people believes that education is more important than ever before. In my opinion, I am agree with this statement because education help people to get better job and improve their life.

First of all, education give people more opportunities in their career. For example, people who have university degree usually earn more money than people without degree. This is because employers prefer to hire educated workers who have good knowledge and skills. Moreover, educated people can work in different fields and have more choices in their life.

Secondly, education help people to develop their personality and become better citizens. When people study, they learn not only academic subjects but also how to think critically and solve problems. This skills are very useful in daily life and help people to make good decisions.

In conclusion, I believe that education is extremely important in modern society. It give people better opportunities and help them to become successful in their life. Therefore, governments should invest more money in education system to ensure that all people have access to quality education.
";

echo "Test Essay:\n" . trim($testEssay) . "\n\n";
echo "Word Count: " . str_word_count($testEssay) . " words\n\n";

// Manual error detection to show what the improved prompt should find
$errors = [
    ["people believes" => "people believe", "type" => "grammar", "explanation" => "Subject-verb agreement error"],
    ["I am agree" => "I agree", "type" => "grammar", "explanation" => "Unnecessary auxiliary verb"],
    ["education help" => "education helps", "type" => "grammar", "explanation" => "Subject-verb agreement error"],
    ["get better job" => "get a better job", "type" => "grammar", "explanation" => "Missing article"],
    ["education give" => "education gives", "type" => "grammar", "explanation" => "Subject-verb agreement error"],
    ["university degree" => "a university degree", "type" => "grammar", "explanation" => "Missing article"],
    ["people without degree" => "people without a degree", "type" => "grammar", "explanation" => "Missing article"],
    ["education help" => "education helps", "type" => "grammar", "explanation" => "Subject-verb agreement error (repeated)"],
    ["This skills" => "These skills", "type" => "grammar", "explanation" => "Demonstrative pronoun agreement"],
    ["It give" => "It gives", "type" => "grammar", "explanation" => "Subject-verb agreement error"],
    ["in education system" => "in the education system", "type" => "grammar", "explanation" => "Missing article"]
];

echo "=== ERRORS THAT SHOULD BE DETECTED ===\n";
echo "Total errors found: " . count($errors) . "\n\n";

foreach ($errors as $i => $error) {
    $original = array_keys($error)[0];
    $corrected = $error[$original];
    echo ($i + 1) . ". \"$original\" → \"$corrected\"\n";
    echo "   Type: " . $error['type'] . "\n";
    echo "   Explanation: " . $error['explanation'] . "\n\n";
}

// Simulate scoring based on errors
$totalErrors = count($errors);
$wordCount = str_word_count($testEssay);
$errorDensity = ($totalErrors / $wordCount) * 100;

echo "=== SCORING ANALYSIS ===\n";
echo "Error Density: " . round($errorDensity, 2) . " errors per 100 words\n";

// Scoring logic based on improved prompt
if ($errorDensity > 12) {
    $suggestedScore = 5.0;
} elseif ($errorDensity > 8) {
    $suggestedScore = 5.5;
} elseif ($errorDensity > 5) {
    $suggestedScore = 6.0;
} else {
    $suggestedScore = 6.5;
}

echo "Suggested Overall Band Score: " . $suggestedScore . "\n";
echo "Grammar Score (with " . $totalErrors . " errors): 5.0-5.5\n";

echo "\n=== VALIDATION CHECKS ===\n";
echo "✓ Minimum 5 errors found: " . ($totalErrors >= 5 ? "PASS" : "FAIL") . "\n";
echo "✓ Error density appropriate for score: " . ($errorDensity > 5 ? "PASS" : "FAIL") . "\n";
echo "✓ Word count sufficient: " . ($wordCount >= 250 ? "PASS" : "FAIL - Under minimum") . "\n";

echo "\n=== IMPROVED PROMPT FEATURES ===\n";
echo "1. ✓ Systematic error detection protocol\n";
echo "2. ✓ Strict band score guidelines\n";
echo "3. ✓ Minimum error requirements (5-15 errors)\n";
echo "4. ✓ Scoring penalties for each error type\n";
echo "5. ✓ Quality control checklist\n";
echo "6. ✓ Common error examples provided\n";
echo "7. ✓ Validation system for consistency\n";

echo "\n=== EXPECTED IMPROVEMENTS ===\n";
echo "- More consistent error detection\n";
echo "- Lower scores for essays with many errors\n";
echo "- Better alignment between error count and band scores\n";
echo "- Automatic validation and adjustment\n";

echo "\n=== TEST COMPLETED ===\n";
