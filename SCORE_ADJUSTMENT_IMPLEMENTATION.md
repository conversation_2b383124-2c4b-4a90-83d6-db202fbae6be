# Triển Khai Điều Chỉnh Điểm Số -0.5

## 🎯 <PERSON>ục Tiê<PERSON> `overall_band_score` đi 0.5 điểm trong toàn bộ hệ thống để điều chỉnh độ khó của việc chấm điểm.

## 🔧 Cách Triển Khai

### 1. **Sử Dụng Eloquent Accessor**

Thay vì sửa từng view, tôi đã sử dụng Eloquent Accessor trong model `ScoringAttempt` để tự động điều chỉnh điểm khi truy cập.

#### File: `app/Models/ScoringAttempt.php`

```php
/**
 * Get overall band score with -0.5 adjustment
 */
public function getOverallBandScoreAttribute($value)
{
    if ($value === null) {
        return null;
    }
    
    // Giảm 0.5 điểm và đảm bảo không âm
    $adjustedScore = max(0, (float) $value - 0.5);
    
    // Làm tròn đến 0.5 gầ<PERSON> nh<PERSON> (IELTS standard)
    return round($adjustedScore * 2) / 2;
}

/**
 * Get original overall band score (without adjustment)
 */
public function getOriginalOverallBandScore()
{
    return $this->attributes['overall_band_score'] ?? null;
}
```

### 2. **Lợi Ích Của Cách Này**

#### ✅ **Toàn Hệ Thống**
- Tất cả nơi sử dụng `$attempt->overall_band_score` đều tự động được điều chỉnh
- Không cần sửa từng view một
- Nhất quán trong toàn bộ ứng dụng

#### ✅ **An Toàn**
- Dữ liệu gốc vẫn được lưu trong database
- Có thể hoàn tác dễ dàng
- Có method `getOriginalOverallBandScore()` để lấy điểm gốc khi cần

#### ✅ **Tuân Thủ IELTS Standard**
- Làm tròn đến 0.5 gần nhất (0.0, 0.5, 1.0, 1.5, ...)
- Đảm bảo điểm không âm
- Giữ nguyên format IELTS

### 3. **Ví Dụ Hoạt Động**

#### Trước Điều Chỉnh:
```
Database: 6.5
Hiển thị: 6.5
```

#### Sau Điều Chỉnh:
```
Database: 6.5 (không thay đổi)
Hiển thị: 6.0 (6.5 - 0.5 = 6.0)
```

#### Các Trường Hợp Đặc Biệt:
```
Database: 1.0 → Hiển thị: 0.5
Database: 0.5 → Hiển thị: 0.0
Database: 0.0 → Hiển thị: 0.0 (không âm)
Database: 6.7 → Hiển thị: 6.0 (6.2 làm tròn thành 6.0)
Database: 6.8 → Hiển thị: 6.5 (6.3 làm tròn thành 6.5)
```

### 4. **Các Nơi Được Ảnh Hưởng**

#### ✅ **Tự Động Điều Chỉnh:**
- Dashboard: `{{ $attempt->overall_band_score }}`
- Show page: `{{ $attempt->overall_band_score }}`
- History page: `{{ $attempt->overall_band_score }}`
- API responses: `$attempt->overall_band_score`
- Email notifications: `{{ $attempt->overall_band_score }}`
- Share functions: `{{ $attempt->overall_band_score }}`

#### ⚠️ **Cần Chú Ý:**
- Logging: Đã cập nhật để hiển thị cả điểm gốc và điểm điều chỉnh
- Database queries: Vẫn sử dụng điểm gốc
- Calculations: Cần sử dụng `getOriginalOverallBandScore()` nếu cần điểm gốc

### 5. **Cập Nhật Logging**

#### File: `app/Http/Controllers/ScoringController.php`

```php
\Log::info('Essay rescoring completed', [
    'user_id' => $user->id,
    'attempt_id' => $attempt->id,
    'old_score_original' => $attempt->getOriginalOverallBandScore(),
    'old_score_displayed' => $attempt->overall_band_score,
    'new_score_original' => $result['overall_band_score'] ?? 'N/A',
    'new_score_displayed' => $result['overall_band_score'] ? max(0, $result['overall_band_score'] - 0.5) : 'N/A'
]);
```

## 📊 Kiểm Tra Hoạt Động

### Test Cases:

1. **Dashboard**: Kiểm tra điểm hiển thị đã giảm 0.5
2. **Show Page**: Kiểm tra overall score và share functions
3. **History**: Kiểm tra danh sách bài thi
4. **API**: Kiểm tra response JSON
5. **Database**: Đảm bảo dữ liệu gốc không thay đổi

### SQL Kiểm Tra:
```sql
-- Kiểm tra dữ liệu gốc vẫn nguyên
SELECT id, overall_band_score FROM scoring_attempts WHERE overall_band_score IS NOT NULL;

-- So sánh với hiển thị trong ứng dụng
```

## 🔄 Hoàn Tác (Nếu Cần)

Để hoàn tác thay đổi, chỉ cần xóa accessor:

```php
// Xóa method này trong ScoringAttempt.php
public function getOverallBandScoreAttribute($value)
{
    // ... xóa toàn bộ method này
}
```

## ⚠️ Lưu Ý Quan Trọng

### 1. **Database Queries**
Khi query database với điều kiện về `overall_band_score`, cần nhớ rằng:
```php
// ❌ Sai - sẽ so sánh với điểm đã điều chỉnh
$attempts = ScoringAttempt::where('overall_band_score', '>', 6.0)->get();

// ✅ Đúng - so sánh với điểm gốc
$attempts = ScoringAttempt::whereRaw('overall_band_score > ?', [6.5])->get();
```

### 2. **API Responses**
API sẽ trả về điểm đã điều chỉnh. Nếu cần điểm gốc, thêm field riêng:
```php
return [
    'overall_band_score' => $attempt->overall_band_score, // Điểm điều chỉnh
    'original_score' => $attempt->getOriginalOverallBandScore(), // Điểm gốc
];
```

### 3. **Calculations**
Khi tính toán dựa trên điểm số, sử dụng điểm gốc:
```php
// ✅ Đúng cho calculations
$originalScore = $attempt->getOriginalOverallBandScore();
$average = collect($attempts)->avg(function($attempt) {
    return $attempt->getOriginalOverallBandScore();
});
```

## 🚀 Triển Khai

### Bước 1: Clear Cache
```bash
php artisan optimize:clear
composer dump-autoload
```

### Bước 2: Test
- Kiểm tra dashboard
- Kiểm tra show page
- Kiểm tra API responses

### Bước 3: Monitor
- Theo dõi logs
- Kiểm tra user feedback
- Đảm bảo không có lỗi

---

**Kết luận**: Thay đổi đã được triển khai an toàn và toàn diện, ảnh hưởng đến toàn bộ hệ thống mà không làm mất dữ liệu gốc.
