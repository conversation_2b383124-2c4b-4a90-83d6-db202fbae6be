<?php
require_once 'config.php';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Progress Dashboard - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .dashboard-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 1400px;
            padding: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .recommendation-card {
            border-left: 4px solid;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            background: white;
        }
        
        .recommendation-card.urgent {
            border-left-color: #dc3545;
            background: #fff5f5;
        }
        
        .recommendation-card.important {
            border-left-color: #ffc107;
            background: #fffbf0;
        }
        
        .recommendation-card.focus {
            border-left-color: #0dcaf0;
            background: #f0fcff;
        }
        
        .recommendation-card.warning {
            border-left-color: #fd7e14;
            background: #fff8f0;
        }
        
        .error-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
            margin: 2px;
        }
        
        .error-badge.grammar {
            background: #dc3545;
            color: white;
        }
        
        .error-badge.vocabulary {
            background: #ffc107;
            color: #333;
        }
        
        .error-badge.coherence {
            background: #0dcaf0;
            color: white;
        }
        
        .error-badge.task_response {
            background: #6f42c1;
            color: white;
        }
        
        .session-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #667eea;
        }
        
        .btn-dashboard {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            color: white;
            padding: 10px 20px;
            margin: 5px;
        }
        
        .btn-dashboard:hover {
            background: linear-gradient(135deg, #5a6fd8, #6a4190);
            color: white;
        }
        
        .no-data {
            text-align: center;
            padding: 40px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="dashboard-container">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1><i class="fas fa-chart-line"></i> Progress Dashboard</h1>
                    <p class="text-muted">Track your IELTS Writing improvement journey</p>
                </div>
                <div>
                    <a href="index.php" class="btn btn-dashboard">
                        <i class="fas fa-edit"></i> New Essay
                    </a>
                    <button class="btn btn-dashboard" onclick="exportProgress()">
                        <i class="fas fa-download"></i> Export Data
                    </button>
                    <button class="btn btn-outline-danger" onclick="clearProgress()">
                        <i class="fas fa-trash"></i> Clear Data
                    </button>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row" id="statsContainer">
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <div class="stat-number text-primary" id="totalEssays">-</div>
                        <div class="stat-label">Total Essays</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <div class="stat-number text-success" id="averageScore">-</div>
                        <div class="stat-label">Average Score</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <div class="stat-number text-info" id="improvement">-</div>
                        <div class="stat-label">Improvement</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <div class="stat-number text-warning" id="recentTrend">-</div>
                        <div class="stat-label">Recent Trend</div>
                    </div>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="row">
                <div class="col-md-8">
                    <div class="chart-container">
                        <h4><i class="fas fa-chart-area"></i> Score Progress</h4>
                        <canvas id="progressChart" width="400" height="200"></canvas>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="chart-container">
                        <h4><i class="fas fa-chart-pie"></i> Error Distribution</h4>
                        <canvas id="errorChart" width="200" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- Skills Analysis -->
            <div class="row">
                <div class="col-md-6">
                    <div class="chart-container">
                        <h4><i class="fas fa-chart-bar"></i> Skills Breakdown</h4>
                        <canvas id="skillsChart" width="300" height="200"></canvas>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="chart-container">
                        <h4><i class="fas fa-lightbulb"></i> Learning Recommendations</h4>
                        <div id="recommendationsContainer">
                            <div class="no-data">
                                <i class="fas fa-info-circle fa-3x mb-3"></i>
                                <p>Complete a few essays to get personalized recommendations</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Sessions -->
            <div class="chart-container">
                <h4><i class="fas fa-history"></i> Recent Sessions</h4>
                <div id="recentSessionsContainer">
                    <div class="no-data">
                        <i class="fas fa-clipboard-list fa-3x mb-3"></i>
                        <p>No essays scored yet. <a href="index.php">Start your first essay</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="assets/progress-tracker.js"></script>
    <script>
        let progressTracker;
        let progressChart, errorChart, skillsChart;

        document.addEventListener('DOMContentLoaded', function() {
            progressTracker = new IELTSProgressTracker();
            initializeDashboard();
        });

        function initializeDashboard() {
            updateStatistics();
            createCharts();
            updateRecommendations();
            updateRecentSessions();
        }

        function updateStatistics() {
            const summary = progressTracker.getProgressSummary();
            
            document.getElementById('totalEssays').textContent = summary.totalEssays;
            document.getElementById('averageScore').textContent = 
                summary.averageScore > 0 ? summary.averageScore.toFixed(1) : '-';
            
            const improvementEl = document.getElementById('improvement');
            if (summary.improvement !== 0) {
                const sign = summary.improvement > 0 ? '+' : '';
                improvementEl.textContent = sign + summary.improvement.toFixed(1);
                improvementEl.className = 'stat-number ' + (summary.improvement > 0 ? 'text-success' : 'text-danger');
            } else {
                improvementEl.textContent = '-';
            }
            
            document.getElementById('recentTrend').textContent = summary.recentTrend;
        }

        function createCharts() {
            createProgressChart();
            createErrorChart();
            createSkillsChart();
        }

        function createProgressChart() {
            const ctx = document.getElementById('progressChart').getContext('2d');
            const data = progressTracker.progressData.improvementTrend;
            
            progressChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.map(d => new Date(d.date).toLocaleDateString()),
                    datasets: [{
                        label: 'Band Score',
                        data: data.map(d => d.score),
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: false,
                            min: 0,
                            max: 9,
                            ticks: {
                                stepSize: 0.5
                            }
                        }
                    }
                }
            });
        }

        function createErrorChart() {
            const ctx = document.getElementById('errorChart').getContext('2d');
            const errors = progressTracker.progressData.commonErrors;
            
            const labels = Object.keys(errors);
            const data = Object.values(errors).map(e => e.count);
            
            errorChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels.map(l => l.charAt(0).toUpperCase() + l.slice(1)),
                    datasets: [{
                        data: data,
                        backgroundColor: [
                            '#dc3545',
                            '#ffc107',
                            '#0dcaf0',
                            '#6f42c1'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function createSkillsChart() {
            const ctx = document.getElementById('skillsChart').getContext('2d');
            const averages = progressTracker.calculateCriteriaAverages();
            
            const labels = Object.keys(averages).map(key => 
                progressTracker.formatCriteriaName(key)
            );
            const data = Object.values(averages);
            
            skillsChart = new Chart(ctx, {
                type: 'radar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Average Score',
                        data: data,
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.2)',
                        pointBackgroundColor: '#667eea'
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        r: {
                            beginAtZero: true,
                            min: 0,
                            max: 9,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                }
            });
        }

        function updateRecommendations() {
            const recommendations = progressTracker.getLearningRecommendations();
            const container = document.getElementById('recommendationsContainer');
            
            if (recommendations.length === 0) {
                return; // Keep the no-data message
            }
            
            container.innerHTML = '';
            recommendations.forEach(rec => {
                const card = document.createElement('div');
                card.className = `recommendation-card ${rec.type}`;
                card.innerHTML = `
                    <h6>${rec.title}</h6>
                    <p class="mb-2">${rec.description}</p>
                    <ul class="mb-0">
                        ${rec.actions.map(action => `<li>${action}</li>`).join('')}
                    </ul>
                `;
                container.appendChild(card);
            });
        }

        function updateRecentSessions() {
            const sessions = progressTracker.progressData.sessions.slice(0, 10);
            const container = document.getElementById('recentSessionsContainer');
            
            if (sessions.length === 0) {
                return; // Keep the no-data message
            }
            
            container.innerHTML = '';
            sessions.forEach(session => {
                const sessionEl = document.createElement('div');
                sessionEl.className = 'session-item';
                sessionEl.innerHTML = `
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6>${session.taskType.toUpperCase()} - Score: ${session.overallScore}</h6>
                            <p class="mb-1">${session.essayPreview}</p>
                            <small class="text-muted">
                                ${new Date(session.date).toLocaleString()} | 
                                ${session.wordCount} words | 
                                ${session.corrections.length} corrections
                            </small>
                        </div>
                        <div>
                            ${session.corrections.slice(0, 3).map(c => 
                                `<span class="error-badge ${c.error_type}">${c.error_type}</span>`
                            ).join('')}
                        </div>
                    </div>
                `;
                container.appendChild(sessionEl);
            });
        }

        function exportProgress() {
            progressTracker.exportData();
        }

        function clearProgress() {
            if (progressTracker.clearData()) {
                location.reload();
            }
        }

        // Refresh dashboard every 30 seconds if on the page
        setInterval(() => {
            if (document.visibilityState === 'visible') {
                initializeDashboard();
            }
        }, 30000);
    </script>
</body>
</html>
