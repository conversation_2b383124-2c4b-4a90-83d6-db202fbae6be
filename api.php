<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';
require_once 'IELTSScorer.php';

/**
 * Ultra clean API response - GUARANTEED clean text
 */
function ultraCleanApiResponse($result) {
    if (!isset($result['highlighted_corrections']) || !is_array($result['highlighted_corrections'])) {
        return $result;
    }

    $cleanedCorrections = [];

    foreach ($result['highlighted_corrections'] as $correction) {
        $originalText = extractCleanText($correction['original_text'] ?? '');
        $suggestedText = extractCleanText($correction['suggested_correction'] ?? '');
        $explanation = extractCleanText($correction['explanation'] ?? '');

        // Only include if we have valid clean text
        if (!empty($originalText) && !empty($suggestedText) && strlen($originalText) > 2) {
            $cleanedCorrections[] = [
                'original_text' => $originalText,
                'suggested_correction' => $suggestedText,
                'error_type' => $correction['error_type'] ?? 'grammar',
                'explanation' => $explanation,
                'severity' => $correction['severity'] ?? 'medium'
            ];
        }
    }

    $result['highlighted_corrections'] = $cleanedCorrections;
    $result['cleaning_stats'] = [
        'original_count' => count($result['highlighted_corrections'] ?? []),
        'cleaned_count' => count($cleanedCorrections),
        'cleaning_success_rate' => count($cleanedCorrections) > 0 ?
            round((count($cleanedCorrections) / max(1, count($result['highlighted_corrections'] ?? []))) * 100, 2) : 0
    ];

    return $result;
}

/**
 * Extract absolutely clean text from any input
 */
function extractCleanText($text) {
    if (!$text) return '';

    // Step 1: Handle HTML-like structures
    if (strpos($text, 'data-') !== false || strpos($text, '<') !== false) {

        // Method 1: Extract from data-original attribute
        if (preg_match('/data-original="([^"]*)"/', $text, $matches)) {
            if (!empty($matches[1]) && strlen($matches[1]) > 2) {
                $text = $matches[1];
            }
        }

        // Method 2: Extract text after the last >
        if (strpos($text, 'data-') !== false && preg_match('/>[^<>]*$/', $text, $matches)) {
            $extracted = trim(str_replace('>', '', $matches[0]));
            if (!empty($extracted) && strpos($extracted, 'data-') === false) {
                $text = $extracted;
            }
        }

        // Method 3: Find the longest meaningful text part
        if (strpos($text, 'data-') !== false || strpos($text, '<') !== false) {
            $parts = preg_split('/[<>"=]/', $text);
            $longest = '';
            foreach ($parts as $part) {
                $part = trim($part);
                if (strlen($part) > strlen($longest) &&
                    strpos($part, 'data-') === false &&
                    strpos($part, 'correction') === false &&
                    strlen($part) > 2) {
                    $longest = $part;
                }
            }
            if (!empty($longest)) {
                $text = $longest;
            }
        }
    }

    // Step 2: Standard cleaning
    $text = strip_tags($text);
    $text = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    $text = preg_replace('/\s+/', ' ', $text);
    $text = trim($text);

    // Step 3: Final validation - reject if still contains artifacts
    if (strpos($text, 'data-') !== false ||
        strpos($text, '<') !== false ||
        strpos($text, 'correction-') !== false ||
        strpos($text, 'title=') !== false) {
        return '';
    }

    return $text;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => true, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    // Validate required fields
    $essay = $input['essay'] ?? '';
    $taskType = $input['task_type'] ?? '';
    $essayQuestion = $input['essay_question'] ?? '';
    $timeLimit = $input['time_limit'] ?? 40;

    if (empty($essay)) {
        throw new Exception('Essay text is required');
    }

    if (empty($taskType)) {
        throw new Exception('Task type is required');
    }

    if (empty($essayQuestion)) {
        throw new Exception('Essay question is required for accurate assessment');
    }

    // Create scorer and process with comprehensive analysis
    $scorer = new IELTSScorer();
    $result = $scorer->scoreEssayComprehensive($essay, $taskType, $essayQuestion, $timeLimit);

    // ULTRA CLEANING - Guarantee clean response
    $result = ultraCleanApiResponse($result);

    // Add request metadata
    $result['request_metadata'] = [
        'timestamp' => date('Y-m-d H:i:s'),
        'essay_length' => strlen($essay),
        'word_count' => str_word_count($essay),
        'task_type' => $taskType,
        'processing_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'],
        'cleaning_applied' => true
    ];

    // Return JSON response
    echo json_encode($result, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'message' => $e->getMessage(),
        'debug_info' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'timestamp' => date('Y-m-d H:i:s')
        ]
    ], JSON_PRETTY_PRINT);
}
?>
