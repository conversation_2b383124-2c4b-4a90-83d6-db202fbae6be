<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class EssayQuestionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $questions = [
            [
                'title' => 'Task 2: Technology and Education',
                'question' => 'Some people believe that technology has made learning easier and more accessible, while others argue that it has made students lazy and less capable of deep thinking. Discuss both views and give your own opinion.',
                'task_type' => 'task2',
                'time_limit' => 40,
                'min_words' => 250,
                'instructions' => 'Write at least 250 words. Present a clear position and support your arguments with examples.',
                'is_active' => true,
            ],
            [
                'title' => 'Task 2: Environment and Development',
                'question' => 'Economic development is often prioritized over environmental protection. To what extent do you agree or disagree with this statement?',
                'task_type' => 'task2',
                'time_limit' => 40,
                'min_words' => 250,
                'instructions' => 'Write at least 250 words. Give reasons for your answer and include relevant examples.',
                'is_active' => true,
            ],
            [
                'title' => 'Task 1 Academic: Bar Chart',
                'question' => 'The chart below shows the percentage of households in owned and rented accommodation in England and Wales between 1918 and 2011. Summarise the information by selecting and reporting the main features, and make comparisons where relevant.',
                'task_type' => 'task1_academic',
                'time_limit' => 20,
                'min_words' => 150,
                'instructions' => 'Write at least 150 words. Describe the main trends and make relevant comparisons.',
                'is_active' => true,
            ],
            [
                'title' => 'Task 1 General: Complaint Letter',
                'question' => 'You recently bought a piece of equipment for your kitchen but it did not work. You phoned the shop but no action was taken. Write a letter to the shop manager. In your letter: - describe the problem with the equipment - explain what happened when you phoned the shop - say what you would like the manager to do',
                'task_type' => 'task1_general',
                'time_limit' => 20,
                'min_words' => 150,
                'instructions' => 'Write at least 150 words. Use appropriate tone and format for a formal complaint letter.',
                'is_active' => true,
            ],
            [
                'title' => 'Task 2: Work-Life Balance',
                'question' => 'In many countries, people are working longer hours and have less time for family and leisure activities. What are the causes of this trend? What solutions can you suggest?',
                'task_type' => 'task2',
                'time_limit' => 40,
                'min_words' => 250,
                'instructions' => 'Write at least 250 words. Discuss causes and provide practical solutions.',
                'is_active' => true,
            ],
        ];

        foreach ($questions as $question) {
            \App\Models\EssayQuestion::create($question);
        }
    }
}
