<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('scoring_attempts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->unsignedBigInteger('essay_question_id')->nullable();
            $table->text('essay_content');
            $table->enum('task_type', ['task1_academic', 'task1_general', 'task2']);
            $table->integer('time_limit')->default(40);
            $table->decimal('overall_band_score', 2, 1)->nullable();
            $table->decimal('task_achievement', 2, 1)->nullable();
            $table->decimal('coherence_cohesion', 2, 1)->nullable();
            $table->decimal('lexical_resource', 2, 1)->nullable();
            $table->decimal('grammar_accuracy', 2, 1)->nullable();
            $table->json('detailed_feedback')->nullable();
            $table->json('highlighted_corrections')->nullable();
            $table->json('criteria_analysis')->nullable();
            $table->json('statistics')->nullable();
            $table->integer('word_count')->nullable();
            $table->integer('essay_length')->nullable();
            $table->enum('status', ['pending', 'completed', 'failed'])->default('pending');
            $table->text('error_message')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('scoring_attempts');
    }
};
