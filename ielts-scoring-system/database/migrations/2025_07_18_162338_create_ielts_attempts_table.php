<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ielts_attempts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->enum('task_type', ['task1', 'task2']);
            $table->text('essay_content');
            $table->decimal('task_achievement', 2, 1)->nullable();
            $table->decimal('coherence_cohesion', 2, 1)->nullable();
            $table->decimal('lexical_resource', 2, 1)->nullable();
            $table->decimal('grammatical_accuracy', 2, 1)->nullable();
            $table->decimal('overall_score', 2, 1)->nullable();
            $table->text('feedback')->nullable();
            $table->enum('status', ['pending', 'processing', 'completed'])->default('pending');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ielts_attempts');
    }
};
