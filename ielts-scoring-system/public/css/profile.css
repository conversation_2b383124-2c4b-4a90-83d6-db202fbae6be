/* Profile Pages CSS */

/* Reset and Base */
.profile-page {
    background: #f8fafc !important;
    min-height: 100vh !important;
    padding: 2rem 0 !important;
}

.profile-page .container {
    max-width: 1200px !important;
    margin: 0 auto !important;
    padding: 0 15px !important;
}

.profile-page .row {
    display: flex !important;
    flex-wrap: wrap !important;
    margin: 0 -15px !important;
}

.profile-page .col-lg-8 {
    flex: 0 0 66.666667% !important;
    max-width: 66.666667% !important;
    padding: 0 15px !important;
}

@media (max-width: 992px) {
    .profile-page .col-lg-8 {
        flex: 0 0 100% !important;
        max-width: 100% !important;
    }
}

/* Page Header */
.profile-page .page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-radius: 20px !important;
    padding: 2.5rem 2rem !important;
    color: white !important;
    margin-bottom: 2rem !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    position: relative !important;
    overflow: hidden !important;
}

.profile-page .page-header::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="60" r="1" fill="rgba(255,255,255,0.1)"/></svg>') !important;
    opacity: 0.3 !important;
    pointer-events: none !important;
}

.profile-page .page-title {
    font-size: 2rem !important;
    font-weight: 800 !important;
    margin-bottom: 0.5rem !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
}

.profile-page .page-subtitle {
    opacity: 0.9 !important;
    margin: 0 !important;
    font-size: 1.1rem !important;
}

.profile-page .btn-back {
    background: rgba(255, 255, 255, 0.2) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    padding: 0.75rem 1.5rem !important;
    border-radius: 12px !important;
    text-decoration: none !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    transition: all 0.3s ease !important;
}

.profile-page .btn-back:hover {
    background: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    text-decoration: none !important;
}

/* Form Sections */
.profile-page .form-section {
    background: white !important;
    border-radius: 20px !important;
    padding: 2rem !important;
    margin-bottom: 2rem !important;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1) !important;
    border: 1px solid rgba(0,0,0,0.05) !important;
    width: 100% !important;
}

.profile-page .section-title {
    font-size: 1.3rem !important;
    font-weight: 700 !important;
    color: #2d3748 !important;
    margin-bottom: 1.5rem !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
    padding-bottom: 1rem !important;
    border-bottom: 2px solid #f1f5f9 !important;
}

.profile-page .section-title i {
    color: #667eea !important;
    font-size: 1.1rem !important;
}

/* Form Controls */
.profile-page .form-group {
    margin-bottom: 1.5rem !important;
    width: 100% !important;
}

.profile-page .form-label {
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    font-weight: 600 !important;
    color: #374151 !important;
    margin-bottom: 0.75rem !important;
    font-size: 0.95rem !important;
}

.profile-page .form-control {
    border: 2px solid #e5e7eb !important;
    border-radius: 12px !important;
    padding: 0.875rem 1rem !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
    background: #f9fafb !important;
    width: 100% !important;
    display: block !important;
}

.profile-page .form-control:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    background: white !important;
    outline: none !important;
}

.profile-page .form-control.is-invalid {
    border-color: #ef4444 !important;
    background: #fef2f2 !important;
}

.profile-page .form-control[readonly] {
    background: #f3f4f6 !important;
    color: #6b7280 !important;
    cursor: not-allowed !important;
}

/* Buttons */
.profile-page .btn {
    padding: 0.875rem 2rem !important;
    border-radius: 12px !important;
    font-weight: 600 !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.5rem !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
    border: none !important;
    cursor: pointer !important;
}

.profile-page .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
}

.profile-page .btn-primary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3) !important;
    color: white !important;
}

.profile-page .btn-outline-secondary {
    border: 2px solid #6b7280 !important;
    color: #6b7280 !important;
    background: transparent !important;
}

.profile-page .btn-outline-secondary:hover {
    background: #6b7280 !important;
    color: white !important;
    transform: translateY(-2px) !important;
}

/* Quick Actions */
.profile-page .quick-actions {
    background: white !important;
    border-radius: 20px !important;
    padding: 2rem !important;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1) !important;
    border: 1px solid rgba(0,0,0,0.05) !important;
}

.profile-page .quick-actions h3 {
    font-size: 1.3rem !important;
    font-weight: 700 !important;
    color: #2d3748 !important;
    margin-bottom: 1.5rem !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
}

.profile-page .action-link {
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
    padding: 1rem !important;
    color: #4b5563 !important;
    text-decoration: none !important;
    border-radius: 12px !important;
    transition: all 0.3s ease !important;
    margin-bottom: 0.5rem !important;
}

.profile-page .action-link:hover {
    background: #f8fafc !important;
    color: #667eea !important;
    text-decoration: none !important;
    transform: translateX(5px) !important;
}

.profile-page .action-link i {
    color: #667eea !important;
    width: 20px !important;
    text-align: center !important;
}

/* Grid System */
.profile-page .g-4 > * {
    padding: 0.75rem !important;
}

.profile-page .col-md-6 {
    flex: 0 0 50% !important;
    max-width: 50% !important;
}

@media (max-width: 768px) {
    .profile-page .col-md-6 {
        flex: 0 0 100% !important;
        max-width: 100% !important;
    }
    
    .profile-page .page-header {
        flex-direction: column !important;
        text-align: center !important;
        gap: 1rem !important;
    }
    
    .profile-page .page-title {
        font-size: 1.5rem !important;
    }
}

/* Form Actions */
.profile-page .form-actions {
    display: flex !important;
    gap: 1rem !important;
    justify-content: flex-start !important;
    margin-top: 2rem !important;
    padding-top: 2rem !important;
    border-top: 2px solid #f1f5f9 !important;
}

@media (max-width: 576px) {
    .profile-page .form-actions {
        flex-direction: column !important;
    }
    
    .profile-page .btn {
        width: 100% !important;
    }
}
