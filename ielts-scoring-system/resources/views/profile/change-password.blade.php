@extends('layouts.app')

@section('title', 'Đổi mật khẩu')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <!-- Page Header -->
            <div class="page-header" data-aos="fade-up">
                <div class="header-content">
                    <h1 class="page-title">
                        <i class="fas fa-key"></i>
                        Đ<PERSON><PERSON> mật khẩu
                    </h1>
                    <p class="page-subtitle">
                        Cập nhật mật khẩu bảo mật cho tài khoản của bạn
                    </p>
                </div>
            </div>

            <!-- Change Password Form -->
            <div class="password-form-container" data-aos="fade-up" data-aos-delay="100">
                <form method="POST" action="{{ route('profile.change-password.update') }}" class="password-form">
                    @csrf

                    <!-- Security Notice -->
                    <div class="security-notice">
                        <div class="notice-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="notice-content">
                            <h4>B<PERSON>o mật tài khoản</h4>
                            <p>Mật khẩu mạnh giúp bảo vệ tài khoản của bạn khỏi các truy cập trái phép.</p>
                        </div>
                    </div>

                    <!-- Current Password -->
                    <div class="form-group">
                        <label for="current_password" class="form-label">
                            <i class="fas fa-lock"></i>
                            Mật khẩu hiện tại
                        </label>
                        <div class="password-input-group">
                            <input type="password"
                                   class="form-control @error('current_password') is-invalid @enderror"
                                   id="current_password"
                                   name="current_password"
                                   required>
                            <button type="button" class="password-toggle" onclick="togglePassword('current_password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        @error('current_password')
                            <div class="invalid-feedback">
                                {{ $message }}
                            </div>
                        @enderror
                    </div>

                    <!-- New Password -->
                    <div class="form-group">
                        <label for="password" class="form-label">
                            <i class="fas fa-key"></i>
                            Mật khẩu mới
                        </label>
                        <div class="password-input-group">
                            <input type="password"
                                   class="form-control @error('password') is-invalid @enderror"
                                   id="password"
                                   name="password"
                                   required>
                            <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="password-strength" id="password-strength">
                            <div class="strength-bar">
                                <div class="strength-fill" id="strength-fill"></div>
                            </div>
                            <div class="strength-text" id="strength-text">Nhập mật khẩu</div>
                        </div>
                        @error('password')
                            <div class="invalid-feedback">
                                {{ $message }}
                            </div>
                        @enderror
                    </div>

                    <!-- Confirm Password -->
                    <div class="form-group">
                        <label for="password_confirmation" class="form-label">
                            <i class="fas fa-check-circle"></i>
                            Xác nhận mật khẩu mới
                        </label>
                        <div class="password-input-group">
                            <input type="password"
                                   class="form-control"
                                   id="password_confirmation"
                                   name="password_confirmation"
                                   required>
                            <button type="button" class="password-toggle" onclick="togglePassword('password_confirmation')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="password-match" id="password-match"></div>
                    </div>

                    <!-- Password Requirements -->
                    <div class="password-requirements">
                        <h5>Yêu cầu mật khẩu:</h5>
                        <ul class="requirements-list">
                            <li id="req-length">
                                <i class="fas fa-times text-danger"></i>
                                Ít nhất 8 ký tự
                            </li>
                            <li id="req-uppercase">
                                <i class="fas fa-times text-danger"></i>
                                Có ít nhất 1 chữ hoa
                            </li>
                            <li id="req-lowercase">
                                <i class="fas fa-times text-danger"></i>
                                Có ít nhất 1 chữ thường
                            </li>
                            <li id="req-number">
                                <i class="fas fa-times text-danger"></i>
                                Có ít nhất 1 số
                            </li>
                        </ul>
                    </div>

                    <!-- Form Actions -->
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-save"></i>
                            Cập nhật mật khẩu
                        </button>
                        <a href="{{ route('profile.show') }}" class="btn btn-outline-secondary btn-lg">
                            <i class="fas fa-arrow-left"></i>
                            Quay lại
                        </a>
                    </div>
                </form>
            </div>

            <!-- Security Tips -->
            <div class="security-tips" data-aos="fade-up" data-aos-delay="200">
                <h3 class="tips-title">
                    <i class="fas fa-lightbulb"></i>
                    Mẹo bảo mật
                </h3>

                <div class="tips-list">
                    <div class="tip-item">
                        <div class="tip-icon">
                            <i class="fas fa-random"></i>
                        </div>
                        <div class="tip-content">
                            <h5>Sử dụng mật khẩu phức tạp</h5>
                            <p>Kết hợp chữ hoa, chữ thường, số và ký tự đặc biệt</p>
                        </div>
                    </div>

                    <div class="tip-item">
                        <div class="tip-icon">
                            <i class="fas fa-ban"></i>
                        </div>
                        <div class="tip-content">
                            <h5>Không sử dụng thông tin cá nhân</h5>
                            <p>Tránh dùng tên, ngày sinh, số điện thoại làm mật khẩu</p>
                        </div>
                    </div>

                    <div class="tip-item">
                        <div class="tip-icon">
                            <i class="fas fa-sync-alt"></i>
                        </div>
                        <div class="tip-content">
                            <h5>Thay đổi định kỳ</h5>
                            <p>Nên thay đổi mật khẩu 3-6 tháng một lần</p>
                        </div>
                    </div>

                    <div class="tip-item">
                        <div class="tip-icon">
                            <i class="fas fa-user-secret"></i>
                        </div>
                        <div class="tip-content">
                            <h5>Giữ bí mật</h5>
                            <p>Không chia sẻ mật khẩu với bất kỳ ai</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
/* Reset any conflicting styles */
.container {
    max-width: 1200px;
}

.row {
    margin-left: -15px;
    margin-right: -15px;
}

.col-lg-6 {
    padding-left: 15px;
    padding-right: 15px;
}
/* Page Header */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 2.5rem 2rem;
    color: white;
    margin-bottom: 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    opacity: 0.3;
}

.page-header > * {
    position: relative;
    z-index: 1;
}

.page-title {
    font-size: 2rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
}

.page-subtitle {
    opacity: 0.9;
    margin: 0;
    font-size: 1.1rem;
}

/* Password Form Container */
.password-form-container {
    background: white;
    border-radius: 20px;
    padding: 2.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    border: 1px solid rgba(0,0,0,0.05);
}

/* Security Notice */
.security-notice {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #e0f2fe 0%, #f0f9ff 100%);
    border-radius: 12px;
    border: 1px solid #0ea5e9;
    margin-bottom: 2rem;
}

.notice-icon {
    width: 50px;
    height: 50px;
    background: #0ea5e9;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.notice-content h4 {
    font-weight: 700;
    color: #0c4a6e;
    margin-bottom: 0.25rem;
}

.notice-content p {
    color: #075985;
    margin: 0;
    font-size: 0.9rem;
}

/* Form Groups */
.form-group {
    margin-bottom: 2rem;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.75rem;
    font-size: 0.95rem;
}

.password-input-group {
    position: relative;
}

.form-control {
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 0.875rem 3rem 0.875rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f9fafb;
    width: 100%;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: white;
}

.form-control.is-invalid {
    border-color: #ef4444;
    background: #fef2f2;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.password-toggle:hover {
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

/* Password Strength */
.password-strength {
    margin-top: 0.75rem;
}

.strength-bar {
    height: 4px;
    background: #e5e7eb;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.strength-fill {
    height: 100%;
    width: 0%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.strength-text {
    font-size: 0.85rem;
    font-weight: 600;
    text-align: center;
}

/* Password Match */
.password-match {
    margin-top: 0.5rem;
    font-size: 0.85rem;
    font-weight: 600;
}

/* Password Requirements */
.password-requirements {
    background: #f8fafc;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 1px solid #e2e8f0;
}

.password-requirements h5 {
    font-weight: 700;
    color: #374151;
    margin-bottom: 1rem;
    font-size: 1rem;
}

.requirements-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
}

.requirements-list li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #6b7280;
    transition: all 0.3s ease;
}

.requirements-list li.valid {
    color: #059669;
}

.requirements-list li.valid i {
    color: #059669;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    padding-top: 2rem;
    border-top: 1px solid #e2e8f0;
}

.form-actions .btn {
    min-width: 180px;
    padding: 0.875rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.btn-outline-secondary {
    border: 2px solid #6b7280;
    color: #6b7280;
    background: transparent;
}

.btn-outline-secondary:hover {
    background: #6b7280;
    color: white;
    transform: translateY(-2px);
}

/* Security Tips */
.security-tips {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    border: 1px solid rgba(0,0,0,0.05);
}

.tips-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid #e2e8f0;
}

.tips-list {
    display: grid;
    gap: 1rem;
}

.tip-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.tip-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
}

.tip-content h5 {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.25rem;
    font-size: 0.95rem;
}

.tip-content p {
    color: #718096;
    margin: 0;
    font-size: 0.85rem;
    line-height: 1.5;
}

/* Responsive */
@media (max-width: 768px) {
    .page-title {
        font-size: 1.75rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .password-form-container {
        padding: 2rem 1.5rem;
    }

    .security-notice {
        flex-direction: column;
        text-align: center;
    }

    .form-actions {
        flex-direction: column;
    }

    .form-actions .btn {
        width: 100%;
    }

    .requirements-list {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 576px) {
    .password-form-container {
        padding: 1.5rem;
    }

    .security-tips {
        padding: 1.5rem;
    }

    .tip-item {
        flex-direction: column;
        text-align: center;
    }
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle password visibility
    window.togglePassword = function(fieldId) {
        const field = document.getElementById(fieldId);
        const button = field.nextElementSibling;
        const icon = button.querySelector('i');

        if (field.type === 'password') {
            field.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            field.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    };

    // Password strength checker
    const passwordField = document.getElementById('password');
    if (passwordField) {
        passwordField.addEventListener('input', function() {
            const password = this.value;
            const strengthFill = document.getElementById('strength-fill');
            const strengthText = document.getElementById('strength-text');

            let strength = 0;
            let strengthLabel = '';
            let strengthColor = '';

            // Check requirements
            const hasLength = password.length >= 8;
            const hasUpper = /[A-Z]/.test(password);
            const hasLower = /[a-z]/.test(password);
            const hasNumber = /\d/.test(password);

            // Update requirement indicators
            updateRequirement('req-length', hasLength);
            updateRequirement('req-uppercase', hasUpper);
            updateRequirement('req-lowercase', hasLower);
            updateRequirement('req-number', hasNumber);

            // Calculate strength
            if (hasLength) strength += 25;
            if (hasUpper) strength += 25;
            if (hasLower) strength += 25;
            if (hasNumber) strength += 25;

            // Set strength label and color
            if (strength === 0) {
                strengthLabel = 'Nhập mật khẩu';
                strengthColor = '#e5e7eb';
            } else if (strength <= 25) {
                strengthLabel = 'Yếu';
                strengthColor = '#ef4444';
            } else if (strength <= 50) {
                strengthLabel = 'Trung bình';
                strengthColor = '#f59e0b';
            } else if (strength <= 75) {
                strengthLabel = 'Mạnh';
                strengthColor = '#3b82f6';
            } else {
                strengthLabel = 'Rất mạnh';
                strengthColor = '#10b981';
            }

            // Update UI
            if (strengthFill && strengthText) {
                strengthFill.style.width = strength + '%';
                strengthFill.style.backgroundColor = strengthColor;
                strengthText.textContent = strengthLabel;
                strengthText.style.color = strengthColor;
            }
        });
    }

    // Password confirmation checker
    const confirmationField = document.getElementById('password_confirmation');
    if (confirmationField) {
        confirmationField.addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmation = this.value;
            const matchDiv = document.getElementById('password-match');

            if (!matchDiv) return;

            if (confirmation === '') {
                matchDiv.textContent = '';
                return;
            }

            if (password === confirmation) {
                matchDiv.textContent = '✓ Mật khẩu khớp';
                matchDiv.style.color = '#10b981';
            } else {
                matchDiv.textContent = '✗ Mật khẩu không khớp';
                matchDiv.style.color = '#ef4444';
            }
        });
    }

    // Helper function for updating requirements
    function updateRequirement(id, isValid) {
        const element = document.getElementById(id);
        if (!element) return;

        const icon = element.querySelector('i');
        if (!icon) return;

        if (isValid) {
            element.classList.add('valid');
            icon.classList.remove('fa-times', 'text-danger');
            icon.classList.add('fa-check', 'text-success');
        } else {
            element.classList.remove('valid');
            icon.classList.remove('fa-check', 'text-success');
            icon.classList.add('fa-times', 'text-danger');
        }
    }
});
</script>
@endpush
