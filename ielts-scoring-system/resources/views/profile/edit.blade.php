@extends('layouts.app')

@section('title', 'Chỉnh sửa thông tin cá nhân')

@push('styles')
<style>
/* TEST CSS - Should make background red */
body {
    background: red !important;
}

/* Profile Pages CSS */
.profile-page {
    background: #f8fafc !important;
    min-height: 100vh !important;
    padding: 2rem 0 !important;
}

.profile-page .container {
    max-width: 1200px !important;
    margin: 0 auto !important;
    padding: 0 15px !important;
}

.profile-page .row {
    display: flex !important;
    flex-wrap: wrap !important;
    margin: 0 -15px !important;
}

.profile-page .col-lg-8 {
    flex: 0 0 66.666667% !important;
    max-width: 66.666667% !important;
    padding: 0 15px !important;
}

@media (max-width: 992px) {
    .profile-page .col-lg-8 {
        flex: 0 0 100% !important;
        max-width: 100% !important;
    }
}

/* Page Header */
.profile-page .page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-radius: 20px !important;
    padding: 2.5rem 2rem !important;
    color: white !important;
    margin-bottom: 2rem !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    position: relative !important;
    overflow: hidden !important;
}

.profile-page .page-title {
    font-size: 2rem !important;
    font-weight: 800 !important;
    margin-bottom: 0.5rem !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
}

.profile-page .page-subtitle {
    opacity: 0.9 !important;
    margin: 0 !important;
    font-size: 1.1rem !important;
}

.profile-page .btn-back {
    background: rgba(255, 255, 255, 0.2) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    padding: 0.75rem 1.5rem !important;
    border-radius: 12px !important;
    text-decoration: none !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    transition: all 0.3s ease !important;
}

.profile-page .btn-back:hover {
    background: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    text-decoration: none !important;
}

/* Form Sections */
.profile-page .form-section {
    background: white !important;
    border-radius: 20px !important;
    padding: 2rem !important;
    margin-bottom: 2rem !important;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1) !important;
    border: 1px solid rgba(0,0,0,0.05) !important;
    width: 100% !important;
}

.profile-page .section-title {
    font-size: 1.3rem !important;
    font-weight: 700 !important;
    color: #2d3748 !important;
    margin-bottom: 1.5rem !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
    padding-bottom: 1rem !important;
    border-bottom: 2px solid #f1f5f9 !important;
}

.profile-page .section-title i {
    color: #667eea !important;
    font-size: 1.1rem !important;
}

/* Form Controls */
.profile-page .form-group {
    margin-bottom: 1.5rem !important;
    width: 100% !important;
}

.profile-page .form-label {
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    font-weight: 600 !important;
    color: #374151 !important;
    margin-bottom: 0.75rem !important;
    font-size: 0.95rem !important;
}

.profile-page .form-control {
    border: 2px solid #e5e7eb !important;
    border-radius: 12px !important;
    padding: 0.875rem 1rem !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
    background: #f9fafb !important;
    width: 100% !important;
    display: block !important;
}

.profile-page .form-control:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    background: white !important;
    outline: none !important;
}

.profile-page .form-control[readonly] {
    background: #f3f4f6 !important;
    color: #6b7280 !important;
    cursor: not-allowed !important;
}

/* Buttons */
.profile-page .btn {
    padding: 0.875rem 2rem !important;
    border-radius: 12px !important;
    font-weight: 600 !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.5rem !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
    border: none !important;
    cursor: pointer !important;
}

.profile-page .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
}

.profile-page .btn-primary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3) !important;
    color: white !important;
}

.profile-page .btn-outline-secondary {
    border: 2px solid #6b7280 !important;
    color: #6b7280 !important;
    background: transparent !important;
}

.profile-page .btn-outline-secondary:hover {
    background: #6b7280 !important;
    color: white !important;
    transform: translateY(-2px) !important;
}

/* Form Actions */
.profile-page .form-actions {
    display: flex !important;
    gap: 1rem !important;
    justify-content: flex-start !important;
    margin-top: 2rem !important;
    padding-top: 2rem !important;
    border-top: 2px solid #f1f5f9 !important;
}

/* Quick Actions */
.profile-page .quick-actions {
    background: white !important;
    border-radius: 20px !important;
    padding: 2rem !important;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1) !important;
    border: 1px solid rgba(0,0,0,0.05) !important;
}

.profile-page .quick-actions h3 {
    font-size: 1.3rem !important;
    font-weight: 700 !important;
    color: #2d3748 !important;
    margin-bottom: 1.5rem !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
}

.profile-page .action-link {
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
    padding: 1rem !important;
    color: #4b5563 !important;
    text-decoration: none !important;
    border-radius: 12px !important;
    transition: all 0.3s ease !important;
    margin-bottom: 0.5rem !important;
}

.profile-page .action-link:hover {
    background: #f8fafc !important;
    color: #667eea !important;
    text-decoration: none !important;
    transform: translateX(5px) !important;
}

.profile-page .action-link i {
    color: #667eea !important;
    width: 20px !important;
    text-align: center !important;
}

/* Grid System */
.profile-page .g-4 > * {
    padding: 0.75rem !important;
}

.profile-page .col-md-6 {
    flex: 0 0 50% !important;
    max-width: 50% !important;
}

@media (max-width: 768px) {
    .profile-page .col-md-6 {
        flex: 0 0 100% !important;
        max-width: 100% !important;
    }

    .profile-page .page-header {
        flex-direction: column !important;
        text-align: center !important;
        gap: 1rem !important;
    }

    .profile-page .page-title {
        font-size: 1.5rem !important;
    }

    .profile-page .form-actions {
        flex-direction: column !important;
    }

    .profile-page .btn {
        width: 100% !important;
    }
}
</style>
@endpush

@section('content')
<div class="profile-page">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Page Header -->
                <div class="page-header">
                    <div class="header-content">
                        <h1 class="page-title">
                            <i class="fas fa-user-edit"></i>
                            Chỉnh sửa thông tin cá nhân
                        </h1>
                        <p class="page-subtitle">
                            Cập nhật thông tin tài khoản của bạn
                        </p>
                    </div>
                    <div class="header-actions">
                        <a href="{{ route('profile.show') }}" class="btn-back">
                            <i class="fas fa-arrow-left"></i>
                            Quay lại
                        </a>
                    </div>
                </div>

                <!-- Success Message -->
                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                <!-- Edit Form -->
                <form method="POST" action="{{ route('profile.update') }}">
                    @csrf
                    @method('PUT')

                    <!-- Form Fields -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-info-circle"></i>
                            Thông tin cơ bản
                        </h3>

                        <div class="row g-4">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name" class="form-label">
                                        <i class="fas fa-user"></i>
                                        Họ và tên
                                    </label>
                                    <input type="text"
                                           class="form-control @error('name') is-invalid @enderror"
                                           id="name"
                                           name="name"
                                           value="{{ old('name', $user->name) }}"
                                           required>
                                    @error('name')
                                        <div class="invalid-feedback">
                                            {{ $message }}
                                        </div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="email" class="form-label">
                                        <i class="fas fa-envelope"></i>
                                        Email
                                    </label>
                                    <input type="email"
                                           class="form-control @error('email') is-invalid @enderror"
                                           id="email"
                                           name="email"
                                           value="{{ old('email', $user->email) }}"
                                           required>
                                    @error('email')
                                        <div class="invalid-feedback">
                                            {{ $message }}
                                        </div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Account Info (Read-only) -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-shield-alt"></i>
                            Thông tin tài khoản
                        </h3>

                        <div class="row g-4">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="role" class="form-label">
                                        <i class="fas fa-user-tag"></i>
                                        Vai trò
                                    </label>
                                    <input type="text"
                                           class="form-control"
                                           id="role"
                                           value="{{ ucfirst($user->role) }}"
                                           readonly>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="created_at" class="form-label">
                                        <i class="fas fa-calendar-alt"></i>
                                        Ngày tham gia
                                    </label>
                                    <input type="text"
                                           class="form-control"
                                           id="created_at"
                                           value="{{ $user->created_at_vn }}"
                                           readonly>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="available_credits" class="form-label">
                                        <i class="fas fa-coins"></i>
                                        Credits khả dụng
                                    </label>
                                    <input type="text"
                                           class="form-control"
                                           id="available_credits"
                                           value="{{ $user->getAvailableCredits() }} credits"
                                           readonly>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="total_credits" class="form-label">
                                        <i class="fas fa-chart-line"></i>
                                        Tổng credits
                                    </label>
                                    <input type="text"
                                           class="form-control"
                                           id="total_credits"
                                           value="{{ $user->credit ? $user->credit->credits : 0 }} credits"
                                           readonly>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            Lưu thay đổi
                        </button>
                        <a href="{{ route('profile.show') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                            Hủy bỏ
                        </a>
                    </div>
                </form>

                <!-- Quick Actions -->
                <div class="quick-actions">
                    <h3>
                        <i class="fas fa-bolt"></i>
                        Hành động nhanh
                    </h3>
                    <a href="{{ route('profile.change-password') }}" class="action-link">
                        <i class="fas fa-key"></i>
                        <div>
                            <strong>Đổi mật khẩu</strong>
                            <small class="d-block text-muted">Cập nhật mật khẩu bảo mật cho tài khoản của bạn</small>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
