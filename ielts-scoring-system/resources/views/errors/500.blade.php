@extends('layouts.app')

@section('title', 'Lỗi hệ thống - 500')

@section('content')
<div class="container-fluid py-5">
    <div class="row justify-content-center">
        <div class="col-lg-6 text-center">
            <div class="py-5">
                <div class="display-1 text-danger mb-4">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h1 class="display-4 fw-bold mb-3">500</h1>
                <h2 class="h4 text-muted mb-4">Lỗi hệ thống</h2>
                <p class="lead text-muted mb-5">
                    Xin lỗi, đã xảy ra lỗi trong hệ thống. Chúng tôi đang khắc phục sự cố này.
                </p>
                
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="{{ route('home') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-home me-2"></i>Về trang chủ
                    </a>
                    <button onclick="window.location.reload()" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-redo me-2"></i>Thử lại
                    </button>
                </div>
                
                <div class="mt-5">
                    <p class="text-muted">
                        Nếu lỗi vẫn tiếp tục, vui lòng 
                        <a href="mailto:<EMAIL>" class="text-decoration-none">liên hệ với chúng tôi</a>.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.display-1 i {
    animation: shake 2s ease-in-out infinite;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}
</style>
@endsection
