@extends('layouts.app')

@section('title', 'Trang không tìm thấy - 404')

@section('content')
<div class="container-fluid py-5">
    <div class="row justify-content-center">
        <div class="col-lg-6 text-center">
            <div class="py-5">
                <div class="display-1 text-primary mb-4">
                    <i class="fas fa-robot"></i>
                </div>
                <h1 class="display-4 fw-bold mb-3">404</h1>
                <h2 class="h4 text-muted mb-4">Trang không tìm thấy</h2>
                <p class="lead text-muted mb-5">
                    Xin lỗi, trang bạn đang tìm kiếm không tồn tại hoặc đã bị di chuyển.
                </p>
                
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="{{ route('home') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-home me-2"></i>Về trang chủ
                    </a>
                    @auth
                        <a href="{{ route('dashboard') }}" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-chart-line me-2"></i>Dashboard
                        </a>
                        <a href="{{ route('scoring.create') }}" class="btn btn-outline-success btn-lg">
                            <i class="fas fa-edit me-2"></i>Chấm thi
                        </a>
                    @else
                        <a href="{{ route('login') }}" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>Đăng nhập
                        </a>
                    @endauth
                </div>
                
                <div class="mt-5">
                    <p class="text-muted">
                        Nếu bạn nghĩ đây là lỗi, vui lòng 
                        <a href="mailto:<EMAIL>" class="text-decoration-none">liên hệ với chúng tôi</a>.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.display-1 i {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}
</style>
@endsection
