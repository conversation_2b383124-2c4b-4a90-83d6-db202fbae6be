@extends('layouts.admin')

@section('title', 'Thêm người dùng - Admin')

@section('content')
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Thêm người dùng mới</h1>
                    <p class="text-muted">Tạo tài khoản người dùng mới trong hệ thống</p>
                </div>
                <div>
                    <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Quay lại
                    </a>
                </div>
            </div>

            <div class="card border-0 shadow-sm">
                <div class="card-body p-4">
                    <form method="POST" action="{{ route('admin.users.store') }}">
                        @csrf

                        <!-- Name -->
                        <div class="mb-3">
                            <label for="name" class="form-label fw-medium">Họ và tên <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror"
                                   id="name" name="name" value="{{ old('name') }}" required autofocus>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Email -->
                        <div class="mb-3">
                            <label for="email" class="form-label fw-medium">Email <span class="text-danger">*</span></label>
                            <input type="email" class="form-control @error('email') is-invalid @enderror"
                                   id="email" name="email" value="{{ old('email') }}" required>
                            @error('email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Password -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label fw-medium">Mật khẩu <span class="text-danger">*</span></label>
                                    <input type="password" class="form-control @error('password') is-invalid @enderror"
                                           id="password" name="password" required>
                                    @error('password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password_confirmation" class="form-label fw-medium">Xác nhận mật khẩu <span class="text-danger">*</span></label>
                                    <input type="password" class="form-control"
                                           id="password_confirmation" name="password_confirmation" required>
                                </div>
                            </div>
                        </div>

                        <!-- Role -->
                        <div class="mb-3">
                            <label for="role" class="form-label fw-medium">Vai trò <span class="text-danger">*</span></label>
                            <select class="form-select @error('role') is-invalid @enderror" id="role" name="role" required>
                                <option value="">Chọn vai trò</option>
                                <option value="user" {{ old('role') === 'user' ? 'selected' : '' }}>User</option>
                                <option value="admin" {{ old('role') === 'admin' ? 'selected' : '' }}>Admin</option>
                            </select>
                            @error('role')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Admin có quyền quản lý toàn bộ hệ thống, User chỉ có thể sử dụng chức năng chấm thi.
                            </div>
                        </div>

                        <!-- Credits -->
                        <div class="mb-4">
                            <label for="credits" class="form-label fw-medium">Số credits ban đầu <span class="text-danger">*</span></label>
                            <input type="number" class="form-control @error('credits') is-invalid @enderror"
                                   id="credits" name="credits" value="{{ old('credits', 10) }}" min="0" required>
                            @error('credits')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">
                                <i class="fas fa-coins me-1"></i>
                                Mỗi lần chấm thi sẽ tiêu tốn 1 credit. Bạn có thể thêm credits sau.
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Hủy
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-user-plus me-2"></i>Tạo người dùng
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Info Card -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-light border-0">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Thông tin quan trọng
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="d-flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-shield-alt text-primary me-2"></i>
                                </div>
                                <div>
                                    <strong>Bảo mật</strong>
                                    <p class="mb-0 small text-muted">Mật khẩu sẽ được mã hóa an toàn. Người dùng có thể thay đổi mật khẩu sau khi đăng nhập.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-envelope text-success me-2"></i>
                                </div>
                                <div>
                                    <strong>Email</strong>
                                    <p class="mb-0 small text-muted">Email sẽ được sử dụng làm tên đăng nhập. Đảm bảo email chính xác và duy nhất.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-coins text-warning me-2"></i>
                                </div>
                                <div>
                                    <strong>Credits</strong>
                                    <p class="mb-0 small text-muted">Credits có thể được thêm hoặc điều chỉnh bất kỳ lúc nào từ trang quản lý người dùng.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-user-cog text-info me-2"></i>
                                </div>
                                <div>
                                    <strong>Vai trò</strong>
                                    <p class="mb-0 small text-muted">Vai trò có thể được thay đổi sau khi tạo tài khoản. Hãy cẩn thận khi cấp quyền Admin.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn {
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
}
</style>

@push('scripts')
<script>
$(document).ready(function() {
    // Auto-generate strong password suggestion
    $('#password').on('focus', function() {
        if ($(this).val() === '') {
            const strongPassword = generateStrongPassword();
            $(this).attr('placeholder', 'Gợi ý: ' + strongPassword);
        }
    });

    function generateStrongPassword() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
        let password = '';
        for (let i = 0; i < 12; i++) {
            password += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return password;
    }

    // Form validation
    $('form').on('submit', function(e) {
        const password = $('#password').val();
        const confirmPassword = $('#password_confirmation').val();

        if (password !== confirmPassword) {
            e.preventDefault();
            alert('Mật khẩu xác nhận không khớp!');
            $('#password_confirmation').focus();
            return false;
        }

        if (password.length < 8) {
            e.preventDefault();
            alert('Mật khẩu phải có ít nhất 8 ký tự!');
            $('#password').focus();
            return false;
        }
    });
});
</script>
@endpush
@endsection
