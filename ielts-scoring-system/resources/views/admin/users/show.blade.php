@extends('layouts.admin')

@section('title', '<PERSON> tiết người dùng - Admin')

@section('content')
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Chi tiết người dùng</h1>
                    <p class="text-muted">Thông tin chi tiết về {{ $user->name }}</p>
                </div>
                <div>
                    <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-primary me-2">
                        <i class="fas fa-edit me-2"></i>Chỉnh sửa
                    </a>
                    <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Quay lại
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- User Info -->
        <div class="col-lg-4 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center p-4">
                    <div class="bg-primary text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                        <i class="fas fa-user fa-2x"></i>
                    </div>
                    <h4 class="fw-bold mb-2">{{ $user->name }}</h4>
                    <p class="text-muted mb-3">{{ $user->email }}</p>
                    <div class="d-flex justify-content-center gap-2 mb-3">
                        <span class="badge bg-{{ $user->role === 'admin' ? 'danger' : 'primary' }} fs-6">
                            {{ ucfirst($user->role) }}
                        </span>
                        <span class="badge bg-{{ $user->is_active ? 'success' : 'secondary' }} fs-6">
                            {{ $user->is_active ? 'Hoạt động' : 'Tạm khóa' }}
                        </span>
                    </div>
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="h5 text-primary mb-0">{{ $user->scoringAttempts->count() }}</div>
                            <small class="text-muted">Bài thi</small>
                        </div>
                        <div class="col-6">
                            <div class="h5 text-success mb-0">{{ $user->getAvailableCredits() }}</div>
                            <small class="text-muted">Credits</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Account Details -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-white border-0 py-3">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Thông tin tài khoản
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">ID:</span>
                            <span class="fw-medium">{{ $user->id }}</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">Ngày tạo:</span>
                            <span class="fw-medium">{{ $user->created_at->format('d/m/Y H:i') }}</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">Cập nhật cuối:</span>
                            <span class="fw-medium">{{ $user->updated_at->format('d/m/Y H:i') }}</span>
                        </div>
                    </div>
                    <div class="mb-0">
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">Email verified:</span>
                            <span class="fw-medium">
                                @if($user->email_verified_at)
                                    <span class="badge bg-success">{{ $user->email_verified_at->format('d/m/Y') }}</span>
                                @else
                                    <span class="badge bg-warning">Chưa xác thực</span>
                                @endif
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Credits Info -->
            @if($user->credit)
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-white border-0 py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-coins me-2"></i>Credits
                        </h6>
                        <button type="button" class="btn btn-sm btn-success"
                                onclick="showAddCreditsModal({{ $user->id }}, '{{ $user->name }}')">
                            <i class="fas fa-plus me-1"></i>Thêm
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="h5 text-primary mb-1">{{ $user->credit->credits }}</div>
                            <small class="text-muted">Tổng</small>
                        </div>
                        <div class="col-4">
                            <div class="h5 text-danger mb-1">{{ $user->credit->used_credits }}</div>
                            <small class="text-muted">Đã dùng</small>
                        </div>
                        <div class="col-4">
                            <div class="h5 text-success mb-1">{{ $user->getAvailableCredits() }}</div>
                            <small class="text-muted">Còn lại</small>
                        </div>
                    </div>
                    @if($user->credit->notes)
                        <div class="mt-3 pt-3 border-top">
                            <small class="text-muted">
                                <strong>Ghi chú:</strong> {{ $user->credit->notes }}
                            </small>
                        </div>
                    @endif
                </div>
            </div>
            @endif
        </div>

        <!-- Scoring Attempts -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">Lịch sử chấm thi</h5>
                </div>
                <div class="card-body p-0">
                    @if($user->scoringAttempts->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th class="border-0">Câu hỏi</th>
                                        <th class="border-0">Loại</th>
                                        <th class="border-0">Band Score</th>
                                        <th class="border-0">Trạng thái</th>
                                        <th class="border-0">Ngày thi</th>
                                        <th class="border-0">Hành động</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($user->scoringAttempts->take(10) as $attempt)
                                    <tr>
                                        <td>
                                            @if($attempt->essayQuestion)
                                                <div class="fw-medium">{{ Str::limit($attempt->essayQuestion->title, 30) }}</div>
                                                <small class="text-muted">{{ Str::limit($attempt->essayQuestion->question, 50) }}</small>
                                            @else
                                                <div class="fw-medium text-muted">Câu hỏi tự do</div>
                                                <small class="text-muted">{{ Str::limit($attempt->essay_content, 50) }}</small>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ ucfirst(str_replace('_', ' ', $attempt->task_type)) }}</span>
                                        </td>
                                        <td>
                                            @if($attempt->isCompleted())
                                                <div class="fw-bold text-primary">{{ $attempt->overall_band_score }}</div>
                                                <small class="text-muted">{{ $attempt->getBandDescription() }}</small>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($attempt->status === 'completed')
                                                <span class="badge bg-success">Hoàn thành</span>
                                            @elseif($attempt->status === 'pending')
                                                <span class="badge bg-warning">Đang xử lý</span>
                                            @else
                                                <span class="badge bg-danger">Lỗi</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div>{{ $attempt->created_at->format('d/m/Y') }}</div>
                                            <small class="text-muted">{{ $attempt->created_at->format('H:i') }}</small>
                                        </td>
                                        <td>
                                            @if($attempt->isCompleted())
                                                <a href="{{ route('scoring.show', $attempt->id) }}" class="btn btn-sm btn-outline-primary" target="_blank">
                                                    <i class="fas fa-eye me-1"></i>Xem
                                                </a>
                                            @endif
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        @if($user->scoringAttempts->count() > 10)
                            <div class="card-footer bg-white border-0 text-center">
                                <small class="text-muted">
                                    Hiển thị 10 bài thi gần nhất. Tổng cộng {{ $user->scoringAttempts->count() }} bài thi.
                                </small>
                            </div>
                        @endif
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Chưa có bài thi nào</h5>
                            <p class="text-muted">Người dùng này chưa thực hiện bài thi nào</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Statistics -->
            @if($user->scoringAttempts->count() > 0)
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">Thống kê</h5>
                </div>
                <div class="card-body">
                    <div class="row g-4">
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 text-primary">{{ $user->scoringAttempts->count() }}</div>
                                <div class="text-muted">Tổng bài thi</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 text-success">{{ $user->scoringAttempts->where('status', 'completed')->count() }}</div>
                                <div class="text-muted">Hoàn thành</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                @php
                                    $avgScore = $user->scoringAttempts->where('status', 'completed')->avg('overall_band_score');
                                @endphp
                                <div class="h4 text-warning">{{ $avgScore ? number_format($avgScore, 1) : '-' }}</div>
                                <div class="text-muted">Điểm TB</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                @php
                                    $maxScore = $user->scoringAttempts->where('status', 'completed')->max('overall_band_score');
                                @endphp
                                <div class="h4 text-info">{{ $maxScore ?? '-' }}</div>
                                <div class="text-muted">Cao nhất</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Add Credits Modal -->
<div class="modal fade" id="addCreditsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Thêm Credits</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addCreditsForm" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Người dùng</label>
                        <input type="text" class="form-control" value="{{ $user->name }}" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Số credits thêm <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" name="credits" min="1" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Ghi chú</label>
                        <textarea class="form-control" name="notes" rows="3" placeholder="Lý do thêm credits..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-coins me-2"></i>Thêm Credits
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
function showAddCreditsModal(userId, userName) {
    $('#addCreditsForm').attr('action', `/admin/users/${userId}/add-credits`);
    $('#addCreditsModal').modal('show');
}
</script>
@endpush
@endsection
