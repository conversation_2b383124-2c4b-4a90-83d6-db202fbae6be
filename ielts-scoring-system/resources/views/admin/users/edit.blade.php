@extends('layouts.admin')

@section('title', 'Chỉnh sửa người dùng - Admin')

@section('content')
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Chỉnh sửa người dùng</h1>
                    <p class="text-muted">Cập nhật thông tin cho {{ $user->name }}</p>
                </div>
                <div>
                    <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Quay lại
                    </a>
                </div>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <div class="card border-0 shadow-sm">
                <div class="card-body p-4">
                    <form method="POST" action="{{ route('admin.users.update', $user) }}">
                        @csrf
                        @method('PUT')

                        <!-- Name -->
                        <div class="mb-3">
                            <label for="name" class="form-label fw-medium">Họ và tên <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror"
                                   id="name" name="name" value="{{ old('name', $user->name) }}" required autofocus>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Email -->
                        <div class="mb-3">
                            <label for="email" class="form-label fw-medium">Email <span class="text-danger">*</span></label>
                            <input type="email" class="form-control @error('email') is-invalid @enderror"
                                   id="email" name="email" value="{{ old('email', $user->email) }}" required>
                            @error('email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Role -->
                        <div class="mb-3">
                            <label for="role" class="form-label fw-medium">Vai trò <span class="text-danger">*</span></label>
                            <select class="form-select @error('role') is-invalid @enderror" id="role" name="role" required>
                                <option value="user" {{ old('role', $user->role) === 'user' ? 'selected' : '' }}>User</option>
                                <option value="admin" {{ old('role', $user->role) === 'admin' ? 'selected' : '' }}>Admin</option>
                            </select>
                            @error('role')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            @if($user->id === auth()->id())
                                <div class="form-text text-warning">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    Bạn đang chỉnh sửa tài khoản của chính mình.
                                </div>
                            @endif
                        </div>

                        <!-- Active Status -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                       {{ old('is_active', $user->is_active) ? 'checked' : '' }}>
                                <label class="form-check-label fw-medium" for="is_active">
                                    Tài khoản hoạt động
                                </label>
                            </div>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Bỏ tick để tạm khóa tài khoản. Người dùng sẽ không thể đăng nhập.
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Hủy
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Cập nhật
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- User Info Card -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-light border-0">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Thông tin tài khoản
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="d-flex justify-content-between">
                                <span class="text-muted">ID:</span>
                                <span class="fw-medium">{{ $user->id }}</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex justify-content-between">
                                <span class="text-muted">Ngày tạo:</span>
                                <span class="fw-medium">{{ $user->created_at->format('d/m/Y H:i') }}</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex justify-content-between">
                                <span class="text-muted">Cập nhật cuối:</span>
                                <span class="fw-medium">{{ $user->updated_at->format('d/m/Y H:i') }}</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex justify-content-between">
                                <span class="text-muted">Email verified:</span>
                                <span class="fw-medium">
                                    @if($user->email_verified_at)
                                        <span class="badge bg-success">Đã xác thực</span>
                                    @else
                                        <span class="badge bg-warning">Chưa xác thực</span>
                                    @endif
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Credits Management -->
            @if($user->credit)
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-light border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-coins me-2"></i>Quản lý Credits
                        </h6>
                        <button type="button" class="btn btn-sm btn-success"
                                onclick="showAddCreditsModal({{ $user->id }}, '{{ $user->name }}')">
                            <i class="fas fa-plus me-1"></i>Thêm Credits
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="text-center">
                                <div class="h4 text-primary">{{ $user->credit->credits }}</div>
                                <div class="text-muted small">Tổng credits</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <div class="h4 text-danger">{{ $user->credit->used_credits }}</div>
                                <div class="text-muted small">Đã sử dụng</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <div class="h4 text-success">{{ $user->getAvailableCredits() }}</div>
                                <div class="text-muted small">Còn lại</div>
                            </div>
                        </div>
                    </div>
                    @if($user->credit->notes)
                        <div class="mt-3">
                            <small class="text-muted">
                                <strong>Ghi chú:</strong> {{ $user->credit->notes }}
                            </small>
                        </div>
                    @endif
                </div>
            </div>
            @endif

            <!-- Password Reset -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-light border-0">
                    <h6 class="mb-0">
                        <i class="fas fa-key me-2"></i>Đặt lại mật khẩu
                    </h6>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">
                        Để đặt lại mật khẩu cho người dùng này, hãy sử dụng chức năng "Quên mật khẩu"
                        hoặc yêu cầu người dùng thay đổi mật khẩu sau khi đăng nhập.
                    </p>
                    <button class="btn btn-outline-warning" onclick="alert('Tính năng đang phát triển')">
                        <i class="fas fa-key me-2"></i>Gửi email đặt lại mật khẩu
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Credits Modal -->
<div class="modal fade" id="addCreditsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Thêm Credits</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addCreditsForm" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Người dùng</label>
                        <input type="text" class="form-control" id="userName" value="{{ $user->name }}" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Số credits thêm <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" name="credits" min="1" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Ghi chú</label>
                        <textarea class="form-control" name="notes" rows="3" placeholder="Lý do thêm credits..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-coins me-2"></i>Thêm Credits
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
function showAddCreditsModal(userId, userName) {
    $('#addCreditsForm').attr('action', `/admin/users/${userId}/add-credits`);
    $('#addCreditsModal').modal('show');
}
</script>
@endpush
@endsection
