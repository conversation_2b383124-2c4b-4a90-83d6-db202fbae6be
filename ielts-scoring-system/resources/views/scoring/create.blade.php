@extends('layouts.app')

@section('title', 'IELTS Writing Scorer - Professional AI Assessment')

@section('content')
<style>
    :root {
        --primary: #667eea;
        --secondary: #764ba2;
        --success: #27ae60;
        --danger: #e74c3c;
        --warning: #f39c12;
        --info: #3498db;
        --dark: #2c3e50;
        --light: #ecf0f1;
        --gradient-primary: linear-gradient(135deg, var(--primary), var(--secondary));
        --gradient-success: linear-gradient(135deg, #11998e, #38ef7d);
        --gradient-danger: linear-gradient(135deg, #ff6b6b, #ee5a24);
        --gradient-warning: linear-gradient(135deg, #f093fb, #f5576c);
        --gradient-info: linear-gradient(135deg, #4facfe, #00f2fe);
        --shadow-soft: 0 10px 40px rgba(0,0,0,0.1);
        --shadow-medium: 0 15px 50px rgba(0,0,0,0.15);
        --shadow-strong: 0 20px 60px rgba(0,0,0,0.2);
    }

    body {
        font-family: 'Inter', sans-serif;
        background: var(--gradient-primary);
        min-height: 100vh;
        color: var(--dark);
        overflow-x: hidden;
    }

    .main-container {
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(20px);
        border-radius: 24px;
        box-shadow: var(--shadow-strong);
        margin: 20px auto;
        max-width: 1600px;
        overflow: hidden;
        position: relative;
    }

    .header-section {
        background: var(--gradient-primary);
        color: white;
        padding: 60px 40px;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .header-section::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: rotate 20s linear infinite;
    }

    @keyframes rotate {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .header-content {
        position: relative;
        z-index: 2;
    }

    .header-title {
        font-size: 3.5rem;
        font-weight: 800;
        margin-bottom: 20px;
        text-shadow: 0 4px 8px rgba(0,0,0,0.2);
        letter-spacing: -1px;
    }

    .header-subtitle {
        font-size: 1.3rem;
        opacity: 0.95;
        font-weight: 400;
        max-width: 600px;
        margin: 0 auto;
    }

    .content-section {
        padding: 50px;
    }

    .form-card, .tips-card {
        background: white;
        border-radius: 20px;
        padding: 35px;
        box-shadow: var(--shadow-soft);
        margin-bottom: 30px;
    }

    .section-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--dark);
        margin-bottom: 25px;
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .tips-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: var(--dark);
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .tips-list {
        list-style: none;
        padding: 0;
    }

    .tips-list li {
        padding: 8px 0;
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 0.95rem;
    }

    .criteria-mini {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 15px;
    }

    .criteria-item-mini {
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid #e9ecef;
    }

    .criteria-item-mini:last-child {
        margin-bottom: 0;
        border-bottom: none;
    }

    .criteria-item-mini strong {
        display: block;
        font-size: 0.9rem;
        color: var(--dark);
    }

    .criteria-item-mini small {
        color: #6c757d;
        font-size: 0.8rem;
    }

    .form-control:focus {
        border-color: var(--primary);
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .form-select:focus {
        border-color: var(--primary);
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    #wordCount {
        font-weight: 600;
        color: var(--primary);
    }

    .btn-primary {
        background: var(--gradient-primary);
        border: none;
        border-radius: 12px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-medium);
    }

    .btn-outline-secondary, .btn-outline-success, .btn-outline-danger {
        border-radius: 12px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-outline-secondary:hover, .btn-outline-success:hover, .btn-outline-danger:hover {
        transform: translateY(-2px);
    }

    .credits-badge {
        position: absolute;
        top: 20px;
        right: 20px;
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        border-radius: 25px;
        padding: 10px 20px;
        color: white;
        font-weight: 600;
        z-index: 3;
    }

    @media (max-width: 768px) {
        .header-title { font-size: 2.5rem; }
        .content-section { padding: 30px 20px; }
        .form-card, .tips-card { padding: 25px; }
        .credits-badge { position: relative; top: auto; right: auto; margin-bottom: 20px; }
    }
</style>

<div class="container-fluid">
    <div class="main-container">
        <!-- Header Section -->
        <div class="header-section">
            <div class="credits-badge">
                <i class="fas fa-coins me-2"></i>{{ auth()->user()->getAvailableCredits() }} credits
            </div>
            <div class="header-content">
                <h1 class="header-title animate__animated animate__fadeInDown">
                    <i class="fas fa-graduation-cap"></i>
                    IELTS Writing Scorer
                </h1>
                <p class="header-subtitle animate__animated animate__fadeInUp animate__delay-1s">
                    Advanced AI-Powered Assessment with Professional Feedback & Interactive Learning
                </p>
            </div>
        </div>

        <!-- Content Section -->
        <div class="content-section">
            <!-- Essay Submission Form -->
            <div class="essay-form-section" id="essayFormSection">
                <div class="row">
                    <div class="col-lg-8">
                        <div class="form-card animate__animated animate__fadeInLeft">
                            <h3 class="section-title">
                                <i class="fas fa-edit"></i>
                                Submit Your Essay for Scoring
                            </h3>
                            <form id="scoringForm">
                                @csrf

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="taskType" class="form-label">Task Type</label>
                                        <select class="form-select" name="task_type" id="taskType" required>
                                            <option value="">Select task type...</option>
                                            <option value="task2" {{ (request('task_type') == 'task2' || ($question && $question->task_type == 'task2')) ? 'selected' : '' }}>Task 2 (Essay)</option>
                                            <option value="task1_academic" {{ (request('task_type') == 'task1_academic' || ($question && $question->task_type == 'task1_academic')) ? 'selected' : '' }}>Task 1 Academic</option>
                                            <option value="task1_general" {{ (request('task_type') == 'task1_general' || ($question && $question->task_type == 'task1_general')) ? 'selected' : '' }}>Task 1 General</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="timeLimit" class="form-label">Time Limit</label>
                                        <select class="form-select" name="time_limit" id="timeLimit">
                                            <option value="40" {{ (!$question || $question->time_limit == 40) ? 'selected' : '' }}>40 minutes (Task 2)</option>
                                            <option value="20" {{ ($question && $question->time_limit == 20) ? 'selected' : '' }}>20 minutes (Task 1)</option>
                                            <option value="60">60 minutes (Practice)</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="questionSelect" class="form-label">
                                        <i class="fas fa-list"></i>
                                        Select Question (Optional)
                                    </label>
                                    <select class="form-select" name="essay_question_id" id="questionSelect">
                                        <option value="">Choose a sample question or write custom...</option>
                                        @foreach($questions as $q)
                                            <option value="{{ $q->id }}"
                                                data-task-type="{{ $q->task_type }}"
                                                data-time-limit="{{ $q->time_limit }}"
                                                data-question="{{ $q->question }}"
                                                {{ ($question && $question->id == $q->id) ? 'selected' : '' }}>
                                                {{ $q->title }} ({{ $q->getTaskTypeLabel() }})
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="essayQuestion" class="form-label">
                                        <i class="fas fa-question-circle"></i>
                                        Essay Question/Topic
                                    </label>
                                    <div id="selectedQuestionDisplay" class="alert alert-info" style="display: {{ $question ? 'block' : 'none' }};">
                                        <strong>{{ $question ? $question->title : '' }}</strong>
                                        <p class="mb-0 mt-2">{{ $question ? $question->question : '' }}</p>
                                        @if($question && $question->instructions)
                                            <small class="text-muted">{{ $question->instructions }}</small>
                                        @endif
                                    </div>
                                    <textarea class="form-control" name="custom_question" id="essayQuestion" rows="4"
                                              placeholder="Paste the complete essay question here..."
                                              style="display: {{ $question ? 'none' : 'block' }};"></textarea>
                                    <div class="form-text">
                                        <i class="fas fa-info-circle"></i>
                                        Copy and paste the exact question from your IELTS test or practice material
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="essayText" class="form-label">Your Essay</label>
                                    <textarea class="form-control" name="essay_content" id="essayText" rows="12"
                                              placeholder="Write your essay here..." required></textarea>
                                    <div class="form-text">
                                        <span id="wordCount">0</span> words | Minimum <span id="minWords">250</span> words recommended for Task 2
                                    </div>
                                </div>

                                <div class="d-flex gap-3 flex-wrap">
                                    <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                                        <i class="fas fa-paper-plane"></i> Score My Essay
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-lg" onclick="loadDemoEssay()">
                                        <i class="fas fa-flask"></i> Load Demo Essay
                                    </button>
                                    <button type="button" class="btn btn-outline-danger btn-lg" onclick="clearForm()">
                                        <i class="fas fa-trash"></i> Clear
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="tips-card animate__animated animate__fadeInRight">
                            <h4 class="tips-title">
                                <i class="fas fa-lightbulb"></i>
                                Writing Tips
                            </h4>
                            <ul class="tips-list">
                                <li><i class="fas fa-check text-success"></i> Write at least 250 words for Task 2</li>
                                <li><i class="fas fa-check text-success"></i> Include clear introduction and conclusion</li>
                                <li><i class="fas fa-check text-success"></i> Use formal academic language</li>
                                <li><i class="fas fa-check text-success"></i> Support ideas with examples</li>
                                <li><i class="fas fa-check text-success"></i> Check grammar and spelling</li>
                                <li><i class="fas fa-check text-success"></i> Manage your time effectively</li>
                            </ul>

                            <div class="scoring-info mt-4">
                                <h5><i class="fas fa-star"></i> Scoring Criteria</h5>
                                <div class="criteria-mini">
                                    <div class="criteria-item-mini">
                                        <strong>Task Achievement</strong>
                                        <small>Address all parts of the task</small>
                                    </div>
                                    <div class="criteria-item-mini">
                                        <strong>Coherence & Cohesion</strong>
                                        <small>Logical organization and linking</small>
                                    </div>
                                    <div class="criteria-item-mini">
                                        <strong>Lexical Resource</strong>
                                        <small>Vocabulary range and accuracy</small>
                                    </div>
                                    <div class="criteria-item-mini">
                                        <strong>Grammar Range & Accuracy</strong>
                                        <small>Sentence structures and accuracy</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Loading Section -->
            <div class="loading-section" id="loadingSection" style="display: none;">
                <div class="loading-card">
                    <div class="loading-animation">
                        <div class="spinner"></div>
                    </div>
                    <h3 class="loading-title">Analyzing Your Essay...</h3>
                    <p class="loading-text">Our AI is carefully reviewing your writing for grammar, vocabulary, coherence, and task achievement.</p>
                    <div class="loading-progress">
                        <div class="progress-bar-loading">
                            <div class="progress-fill-loading"></div>
                        </div>
                        <div class="loading-steps">
                            <div class="step active" id="step1">
                                <i class="fas fa-file-text"></i> Processing text
                            </div>
                            <div class="step" id="step2">
                                <i class="fas fa-search"></i> Analyzing errors
                            </div>
                            <div class="step" id="step3">
                                <i class="fas fa-chart-bar"></i> Calculating scores
                            </div>
                            <div class="step" id="step4">
                                <i class="fas fa-check"></i> Generating report
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .loading-section {
        text-align: center;
        padding: 80px 20px;
    }

    .loading-card {
        background: white;
        border-radius: 24px;
        padding: 50px;
        box-shadow: var(--shadow-medium);
        max-width: 600px;
        margin: 0 auto;
    }

    .loading-animation {
        margin-bottom: 30px;
    }

    .spinner {
        width: 80px;
        height: 80px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid var(--primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .loading-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--dark);
        margin-bottom: 15px;
    }

    .loading-text {
        font-size: 1.1rem;
        color: #6c757d;
        margin-bottom: 40px;
    }

    .progress-bar-loading {
        width: 100%;
        height: 8px;
        background: #e9ecef;
        border-radius: 4px;
        overflow: hidden;
        margin-bottom: 30px;
    }

    .progress-fill-loading {
        height: 100%;
        background: var(--gradient-primary);
        border-radius: 4px;
        width: 0%;
        animation: loadingProgress 8s ease-in-out forwards;
    }

    @keyframes loadingProgress {
        0% { width: 0%; }
        25% { width: 25%; }
        50% { width: 50%; }
        75% { width: 75%; }
        100% { width: 100%; }
    }

    .loading-steps {
        display: flex;
        justify-content: space-between;
        gap: 20px;
    }

    .step {
        flex: 1;
        text-align: center;
        padding: 15px;
        border-radius: 12px;
        background: #f8f9fa;
        color: #6c757d;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }

    .step.active {
        background: var(--gradient-primary);
        color: white;
        transform: scale(1.05);
    }

    .step i {
        display: block;
        font-size: 1.5rem;
        margin-bottom: 8px;
    }

    @media (max-width: 768px) {
        .loading-steps { flex-direction: column; gap: 10px; }
    }
</style>

@push('scripts')
<script>
// Global variables
let currentEssayData = null;
let activeCorrection = -1;

// Initialize application
$(document).ready(function() {
    initializeFormHandlers();
    showFormSection();
});

// Form handling
function initializeFormHandlers() {
    $('#scoringForm').on('submit', handleFormSubmission);
    $('#essayText').on('input', updateWordCount);
    $('#taskType').on('change', updatePromptPlaceholder);
    $('#questionSelect').on('change', handleQuestionSelection);

    // Initialize word count and placeholder
    updateWordCount();
    updatePromptPlaceholder();
}

function updateWordCount() {
    const text = $('#essayText').val();
    const wordCount = text.trim() ? text.trim().split(/\s+/).length : 0;
    $('#wordCount').text(wordCount);

    const wordCountElement = $('#wordCount');
    if (wordCount < 150) {
        wordCountElement.css('color', '#e74c3c');
    } else if (wordCount < 250) {
        wordCountElement.css('color', '#f39c12');
    } else {
        wordCountElement.css('color', '#27ae60');
    }
}

function handleQuestionSelection() {
    const selectedOption = $('#questionSelect option:selected');

    if ($('#questionSelect').val()) {
        const title = selectedOption.text().split(' (')[0];
        const question = selectedOption.data('question');
        const taskType = selectedOption.data('task-type');
        const timeLimit = selectedOption.data('time-limit');

        $('#selectedQuestionDisplay strong').text(title);
        $('#selectedQuestionDisplay p').text(question);
        $('#selectedQuestionDisplay').show();
        $('#essayQuestion').hide().val('');

        $('#taskType').val(taskType);
        $('#timeLimit').val(timeLimit);

        const minWords = taskType === 'task2' ? 250 : 150;
        $('#minWords').text(minWords);
    } else {
        $('#selectedQuestionDisplay').hide();
        $('#essayQuestion').show();
    }
}

function updatePromptPlaceholder() {
    const taskType = $('#taskType').val();
    const minWords = taskType === 'task2' ? 250 : 150;
    $('#minWords').text(minWords);

    let placeholder = "Write your essay here...";
    if (taskType === 'task2') {
        placeholder = "Write your argumentative essay here (minimum 250 words)...";
    } else if (taskType === 'task1_academic') {
        placeholder = "Describe the visual information here (minimum 150 words)...";
    } else if (taskType === 'task1_general') {
        placeholder = "Write your letter here (minimum 150 words)...";
    }

    $('#essayText').attr('placeholder', placeholder);
}

function handleFormSubmission(e) {
    e.preventDefault();

    const essayText = $('#essayText').val().trim();
    const taskType = $('#taskType').val();
    const questionId = $('#questionSelect').val();
    const customQuestion = $('#essayQuestion').val().trim();

    if (!essayText) {
        alert('Please enter your essay text.');
        return;
    }

    if (!taskType) {
        alert('Please select a task type.');
        return;
    }

    if (!questionId && !customQuestion) {
        alert('Please select a question or enter a custom question.');
        return;
    }

    showLoadingSection();

    $.ajax({
        url: '{{ route("scoring.store") }}',
        method: 'POST',
        data: $('#scoringForm').serialize(),
        success: function(response) {
            if (response.success) {
                window.location.href = response.redirect_url;
            } else {
                alert(response.message || 'An error occurred.');
                showFormSection();
            }
        },
        error: function(xhr) {
            const response = xhr.responseJSON;
            alert(response.message || 'An error occurred while scoring.');
            showFormSection();
        }
    });
}

function showFormSection() {
    $('#essayFormSection').show();
    $('#loadingSection').hide();
}

function showLoadingSection() {
    $('#essayFormSection').hide();
    $('#loadingSection').show();

    // Animate loading steps
    setTimeout(() => $('#step2').addClass('active'), 2000);
    setTimeout(() => $('#step3').addClass('active'), 4000);
    setTimeout(() => $('#step4').addClass('active'), 6000);
}

function loadDemoEssay() {
    const demoEssay = `Some people believe that technology has made learning easier and more accessible, while others argue that it has made students lazy and less capable of deep thinking. In my opinion, while technology has certainly transformed education in positive ways, it has also created some concerning dependencies that may hinder critical thinking skills.

On the positive side, technology has democratized access to education. Online courses, educational videos, and digital libraries have made high-quality learning materials available to students worldwide, regardless of their geographical location or economic background. For instance, platforms like Khan Academy and Coursera offer free courses from prestigious universities, enabling millions of students to access education that was previously unavailable to them.

However, the convenience of technology may be creating a generation of students who struggle with deep, sustained thinking. When information is instantly available through search engines, students may become less inclined to memorize important facts or engage in prolonged reflection. This "Google effect" can lead to superficial learning where students know how to find information quickly but struggle to synthesize and analyze it critically.

Furthermore, the constant stimulation from digital devices may be shortening attention spans. Many students now expect immediate gratification and may find it difficult to engage with complex texts or problems that require sustained concentration. This could potentially impact their ability to develop the patience and persistence necessary for deep learning.

In conclusion, while technology has undoubtedly made education more accessible and engaging, educators must be mindful of its potential drawbacks. The key is to use technology as a tool to enhance learning while still encouraging students to develop critical thinking skills and the ability to engage in deep, reflective thought.`;

    $('#essayText').val(demoEssay);
    $('#taskType').val('task2');
    updateWordCount();
    updatePromptPlaceholder();
}

function clearForm() {
    if (confirm('Are you sure you want to clear the form?')) {
        $('#scoringForm')[0].reset();
        $('#selectedQuestionDisplay').hide();
        $('#essayQuestion').show();
        updateWordCount();
    }
}
</script>
@endpush
@endsection
