@extends('layouts.app')

@section('title', 'Lịch sử chấm thi - IELTS AI Scoring System')

@push('styles')
<style>
/* Modern History Page CSS */
.history-page {
    background: #f8fafc;
    min-height: 100vh;
    padding: 2rem 0;
}

.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 2.5rem;
    color: white;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
    opacity: 0.3;
}

.page-header > * {
    position: relative;
    z-index: 1;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.page-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.btn-modern {
    padding: 0.875rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
    border: none;
}

.btn-primary-modern {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-primary-modern:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

/* Stats Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    border: 1px solid rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 60px rgba(0,0,0,0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    font-size: 1.5rem;
    color: white;
}

.stat-icon.total { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.stat-icon.completed { background: linear-gradient(135deg, #10b981 0%, #059669 100%); }
.stat-icon.average { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); }
.stat-icon.recent { background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); }

.stat-value {
    font-size: 2.5rem;
    font-weight: 800;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6b7280;
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Attempts Grid */
.attempts-container {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    border: 1px solid rgba(0,0,0,0.05);
    overflow: hidden;
}

.container-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e2e8f0;
}

.container-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: #2d3748;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.container-title i {
    color: #667eea;
}

.attempts-grid {
    padding: 2rem;
    display: grid;
    gap: 1.5rem;
}

.attempt-card {
    background: #f8fafc;
    border-radius: 15px;
    padding: 1.5rem;
    border-left: 4px solid #667eea;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.attempt-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.attempt-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.attempt-info h4 {
    font-size: 1.1rem;
    font-weight: 700;
    color: #2d3748;
    margin: 0 0 0.5rem 0;
}

.attempt-meta {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-bottom: 1rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.meta-item i {
    color: #667eea;
    width: 16px;
    text-align: center;
}

.attempt-score {
    text-align: center;
    min-width: 80px;
}

.score-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.5rem;
    font-size: 1.2rem;
    font-weight: 800;
    color: white;
}

.score-excellent { background: linear-gradient(135deg, #10b981 0%, #059669 100%); }
.score-good { background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); }
.score-average { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); }
.score-poor { background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); }
.score-pending { background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%); }

.score-label {
    font-size: 0.75rem;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.attempt-actions {
    display: flex;
    gap: 0.75rem;
    margin-top: 1rem;
}

.btn-action {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
    border: none;
    cursor: pointer;
}

.btn-view {
    background: #eff6ff;
    color: #2563eb;
    border: 1px solid #bfdbfe;
}

.btn-view:hover {
    background: #dbeafe;
    color: #1d4ed8;
    text-decoration: none;
    transform: translateY(-1px);
}

.btn-delete {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.btn-delete:hover {
    background: #fee2e2;
    color: #b91c1c;
    transform: translateY(-1px);
}

.btn-disabled {
    background: #f3f4f6;
    color: #9ca3af;
    border: 1px solid #d1d5db;
    cursor: not-allowed;
}

.status-badge {
    padding: 0.375rem 0.875rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-completed {
    background: #f0fdf4;
    color: #16a34a;
    border: 1px solid #bbf7d0;
}

.status-pending {
    background: #fef3c7;
    color: #d97706;
    border: 1px solid #fde68a;
}

.status-failed {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
}

.empty-icon {
    font-size: 4rem;
    color: #d1d5db;
    margin-bottom: 1.5rem;
}

.empty-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #374151;
    margin-bottom: 0.5rem;
}

.empty-subtitle {
    color: #6b7280;
    margin-bottom: 2rem;
}

/* Responsive */
@media (max-width: 768px) {
    .page-title {
        font-size: 2rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .header-actions {
        flex-direction: column;
    }

    .attempt-header {
        flex-direction: column;
        gap: 1rem;
    }

    .attempt-meta {
        flex-direction: column;
        gap: 0.5rem;
    }

    .attempt-actions {
        flex-direction: column;
    }
}
</style>
@endpush

@section('content')
<div class="history-page">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-history"></i>
                Lịch sử chấm thi
            </h1>
            <p class="page-subtitle">
                Theo dõi tiến trình học tập và cải thiện kỹ năng IELTS Writing của bạn
            </p>
            <div class="header-actions">
                <a href="{{ route('scoring.create') }}" class="btn-modern btn-primary-modern">
                    <i class="fas fa-edit"></i>
                    Chấm thi mới
                </a>
            </div>
        </div>

        <!-- Success/Error Messages -->
        @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert" style="border-radius: 12px; border: none; box-shadow: 0 4px 20px rgba(16, 185, 129, 0.2);">
                <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger alert-dismissible fade show" role="alert" style="border-radius: 12px; border: none; box-shadow: 0 4px 20px rgba(220, 38, 38, 0.2);">
                <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif

        <!-- Stats Cards -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon total">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="stat-value">{{ $attempts->total() }}</div>
                <div class="stat-label">Tổng bài thi</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon completed">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-value">{{ $attempts->where('status', 'completed')->count() }}</div>
                <div class="stat-label">Đã hoàn thành</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon average">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-value">
                    @php
                        $avgScore = $attempts->where('status', 'completed')->avg('overall_band_score');
                    @endphp
                    {{ $avgScore ? number_format($avgScore, 1) : '0.0' }}
                </div>
                <div class="stat-label">Điểm trung bình</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon recent">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-value">{{ $attempts->where('created_at', '>=', now()->subDays(7))->count() }}</div>
                <div class="stat-label">Tuần này</div>
            </div>
        </div>

        <!-- Attempts List -->
        <div class="attempts-container">
            <div class="container-header">
                <h3 class="container-title">
                    <i class="fas fa-list"></i>
                    Danh sách bài thi
                </h3>
            </div>

            @if($attempts->count() > 0)
            <div class="attempts-grid">
                @foreach($attempts as $attempt)
                <div class="attempt-card">
                    <div class="attempt-header">
                        <div class="attempt-info">
                            <h4>{{ $attempt->essayQuestion->title ?? 'Custom Question' }}</h4>
                            <div class="attempt-meta">
                                <div class="meta-item">
                                    <i class="fas fa-tag"></i>
                                    <span>{{ ucfirst(str_replace('_', ' ', $attempt->task_type)) }}</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-calendar"></i>
                                    <span>{{ $attempt->created_date_vn }}</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-clock"></i>
                                    <span>{{ $attempt->created_time_vn }}</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-file-word"></i>
                                    <span>{{ $attempt->word_count ?? 0 }} từ</span>
                                </div>
                            </div>
                            <div>
                                @if($attempt->isCompleted())
                                    <span class="status-badge status-completed">Hoàn thành</span>
                                @elseif($attempt->status === 'pending')
                                    <span class="status-badge status-pending">Đang xử lý</span>
                                @else
                                    <span class="status-badge status-failed">Lỗi</span>
                                @endif
                            </div>
                        </div>

                        <div class="attempt-score">
                            @if($attempt->isCompleted())
                                @php
                                    $score = $attempt->overall_band_score;
                                    $scoreClass = 'score-pending';
                                    if ($score >= 7.0) $scoreClass = 'score-excellent';
                                    elseif ($score >= 6.0) $scoreClass = 'score-good';
                                    elseif ($score >= 5.0) $scoreClass = 'score-average';
                                    elseif ($score > 0) $scoreClass = 'score-poor';
                                @endphp
                                <div class="score-circle {{ $scoreClass }}">
                                    {{ $score }}
                                </div>
                                <div class="score-label">Band Score</div>
                            @else
                                <div class="score-circle score-pending">
                                    <i class="fas fa-hourglass-half"></i>
                                </div>
                                <div class="score-label">Chờ xử lý</div>
                            @endif
                        </div>
                    </div>

                    <div class="attempt-actions">
                        @if($attempt->isCompleted())
                            <a href="{{ route('scoring.show', $attempt->getHashid()) }}" class="btn-action btn-view">
                                <i class="fas fa-eye"></i>
                                Xem chi tiết
                            </a>
                        @elseif($attempt->status === 'pending')
                            <button class="btn-action btn-disabled" disabled>
                                <i class="fas fa-spinner fa-spin"></i>
                                Đang xử lý
                            </button>
                        @else
                            <button class="btn-action btn-disabled" disabled>
                                <i class="fas fa-exclamation-triangle"></i>
                                Có lỗi xảy ra
                            </button>
                        @endif

                        <form method="POST" action="{{ route('scoring.destroy', ['attempt' => $attempt->id]) }}" style="display: inline;" onsubmit="return confirm('Bạn có chắc chắn muốn xóa bài thi này?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn-action btn-delete">
                                <i class="fas fa-trash"></i>
                                Xóa
                            </button>
                        </form>
                    </div>
                </div>
                @endforeach
            </div>

            @if($attempts->hasPages())
            <div class="p-3">
                {{ $attempts->links() }}
            </div>
            @endif

            @else
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
                <h3 class="empty-title">Chưa có bài thi nào</h3>
                <p class="empty-subtitle">Bạn chưa thực hiện bài thi IELTS Writing nào. Hãy bắt đầu ngay!</p>
                <a href="{{ route('scoring.create') }}" class="btn-modern btn-primary-modern">
                    <i class="fas fa-edit"></i>
                    Chấm thi đầu tiên
                </a>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection
