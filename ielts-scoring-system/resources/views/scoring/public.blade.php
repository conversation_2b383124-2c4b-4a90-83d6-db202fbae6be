<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IELTS Score Result - Band {{ $attempt->overall_band_score }}</title>

    <!-- Meta tags for social sharing -->
    <meta property="og:title" content="IELTS Score Result - Band {{ $attempt->overall_band_score }}">
    <meta property="og:description" content="Check out this IELTS Writing score: {{ $attempt->getBandDescription() }}">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ request()->url() }}">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }

        .public-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        .public-header {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
        }

        .public-header h1 {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
        }

        .public-header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .score-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .score-header {
            background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .overall-score {
            font-size: 4rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            text-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .score-description {
            font-size: 1.3rem;
            opacity: 0.95;
        }

        .score-body {
            padding: 2rem;
        }

        .criteria-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .criteria-item {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            border-left: 4px solid var(--primary-color);
        }

        .criteria-name {
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 0.5rem;
        }

        .criteria-score {
            font-size: 2rem;
            font-weight: 800;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .criteria-feedback {
            font-size: 0.9rem;
            color: #6c757d;
        }

        .essay-section {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .section-title i {
            color: var(--primary-color);
        }

        .essay-content {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1.5rem;
            line-height: 1.8;
            font-size: 1rem;
            color: var(--dark-color);
            white-space: pre-wrap;
        }

        .feedback-section {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .feedback-item {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid var(--info-color);
        }

        .feedback-title {
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 0.5rem;
        }

        .feedback-content {
            color: #6c757d;
            line-height: 1.6;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }

        .stat-item {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1rem;
            text-align: center;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 0.25rem;
        }

        .public-footer {
            text-align: center;
            color: white;
            margin-top: 3rem;
            padding: 2rem;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
        }

        .cta-button {
            display: inline-block;
            background: white;
            color: var(--primary-color);
            padding: 1rem 2rem;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            margin-top: 1rem;
            transition: all 0.3s ease;
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(255,255,255,0.3);
            color: var(--primary-color);
            text-decoration: none;
        }

        @media (max-width: 768px) {
            .public-header h1 {
                font-size: 2rem;
            }

            .overall-score {
                font-size: 3rem;
            }

            .criteria-grid {
                grid-template-columns: 1fr;
            }

            .score-body,
            .essay-section,
            .feedback-section {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="public-container">
        <!-- Header -->
        <div class="public-header">
            <h1><i class="fas fa-graduation-cap"></i> IELTS Score Result</h1>
            <p>AI-Powered Writing Assessment</p>
        </div>

        <!-- Score Card -->
        <div class="score-card">
            <div class="score-header">
                <div class="overall-score">{{ $attempt->overall_band_score }}</div>
                <div class="score-description">{{ $attempt->getBandDescription() }}</div>
                <div class="mt-2">
                    <span class="badge bg-light text-dark fs-6">CEFR: {{ $attempt->getCefrLevel() }}</span>
                </div>
            </div>

            <div class="score-body">
                <h3 class="section-title">
                    <i class="fas fa-chart-bar"></i>
                    Criteria Breakdown
                </h3>

                <div class="criteria-grid">
                    <div class="criteria-item">
                        <div class="criteria-name">Task Achievement</div>
                        <div class="criteria-score">{{ $attempt->task_achievement ?? '-' }}</div>
                        <div class="criteria-feedback">Response to task requirements</div>
                    </div>
                    <div class="criteria-item">
                        <div class="criteria-name">Coherence & Cohesion</div>
                        <div class="criteria-score">{{ $attempt->coherence_cohesion ?? '-' }}</div>
                        <div class="criteria-feedback">Organization and linking</div>
                    </div>
                    <div class="criteria-item">
                        <div class="criteria-name">Lexical Resource</div>
                        <div class="criteria-score">{{ $attempt->lexical_resource ?? '-' }}</div>
                        <div class="criteria-feedback">Vocabulary range and accuracy</div>
                    </div>
                    <div class="criteria-item">
                        <div class="criteria-name">Grammar & Accuracy</div>
                        <div class="criteria-score">{{ $attempt->grammar_accuracy ?? '-' }}</div>
                        <div class="criteria-feedback">Grammar range and accuracy</div>
                    </div>
                </div>

                @if($attempt->statistics)
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">{{ $attempt->word_count ?? 0 }}</div>
                        <div class="stat-label">Words</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{{ $attempt->task_type }}</div>
                        <div class="stat-label">Task Type</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{{ $attempt->time_limit ?? 40 }}min</div>
                        <div class="stat-label">Time Limit</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{{ $attempt->created_date_vn }}</div>
                        <div class="stat-label">Scored Date</div>
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- Essay Content -->
        @if($attempt->essay_content)
        <div class="essay-section">
            <h3 class="section-title">
                <i class="fas fa-file-alt"></i>
                Essay Content
            </h3>
            <div class="essay-content">{{ $attempt->essay_content }}</div>
        </div>
        @endif

        <!-- Detailed Feedback -->
        @if($attempt->detailed_feedback)
        <div class="feedback-section">
            <h3 class="section-title">
                <i class="fas fa-comments"></i>
                Detailed Feedback
            </h3>

            @foreach($attempt->detailed_feedback as $category => $feedback)
            <div class="feedback-item">
                <div class="feedback-title">{{ ucfirst(str_replace('_', ' ', $category)) }}</div>
                <div class="feedback-content">{{ $feedback }}</div>
            </div>
            @endforeach
        </div>
        @endif

        <!-- Footer CTA -->
        <div class="public-footer">
            <h4><i class="fas fa-rocket"></i> Want to improve your IELTS score?</h4>
            <p>Get AI-powered feedback and detailed analysis for your essays</p>
            <a href="{{ route('home') }}" class="cta-button">
                <i class="fas fa-pen"></i> Try IELTS AI Scorer
            </a>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
