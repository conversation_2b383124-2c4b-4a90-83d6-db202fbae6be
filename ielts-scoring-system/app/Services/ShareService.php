<?php

namespace App\Services;

use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Str;

class ShareService
{
    /**
     * Generate a shareable token for scoring attempt
     */
    public static function generateShareToken(int $attemptId): string
    {
        // Create a unique token with timestamp and random string
        $data = [
            'id' => $attemptId,
            'timestamp' => time(),
            'random' => Str::random(16)
        ];
        
        // Encrypt the data
        $encrypted = Crypt::encrypt($data);
        
        // Make it URL-safe
        return base64_encode($encrypted);
    }
    
    /**
     * Decode share token to get attempt ID
     */
    public static function decodeShareToken(string $token): ?int
    {
        try {
            // Decode from base64
            $encrypted = base64_decode($token);
            
            // Decrypt the data
            $data = Crypt::decrypt($encrypted);
            
            // Validate structure
            if (!is_array($data) || !isset($data['id'])) {
                return null;
            }
            
            return (int) $data['id'];
            
        } catch (\Exception $e) {
            return null;
        }
    }
    
    /**
     * Generate share URL for scoring attempt
     */
    public static function generateShareUrl(int $attemptId): string
    {
        $token = self::generateShareToken($attemptId);
        return route('scoring.public', ['token' => $token]);
    }
    
    /**
     * Validate if token is still valid (optional expiry check)
     */
    public static function isTokenValid(string $token, int $maxAgeHours = null): bool
    {
        try {
            $encrypted = base64_decode($token);
            $data = Crypt::decrypt($encrypted);
            
            if (!is_array($data) || !isset($data['timestamp'])) {
                return false;
            }
            
            // Check expiry if specified
            if ($maxAgeHours !== null) {
                $tokenAge = time() - $data['timestamp'];
                $maxAge = $maxAgeHours * 3600; // Convert hours to seconds
                
                if ($tokenAge > $maxAge) {
                    return false;
                }
            }
            
            return true;
            
        } catch (\Exception $e) {
            return false;
        }
    }
}
