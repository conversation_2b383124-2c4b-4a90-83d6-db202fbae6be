<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class EssayQuestion extends Model
{
    protected $fillable = [
        'title',
        'question',
        'task_type',
        'time_limit',
        'min_words',
        'instructions',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get scoring attempts for this question
     */
    public function scoringAttempts(): HasMany
    {
        return $this->hasMany(ScoringAttempt::class);
    }

    /**
     * Scope for active questions
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for specific task type
     */
    public function scopeTaskType($query, $taskType)
    {
        return $query->where('task_type', $taskType);
    }

    /**
     * Get task type label
     */
    public function getTaskTypeLabel(): string
    {
        return match($this->task_type) {
            'task1_academic' => 'Task 1 Academic',
            'task1_general' => 'Task 1 General Training',
            'task2' => 'Task 2',
            default => 'Unknown'
        };
    }
}
