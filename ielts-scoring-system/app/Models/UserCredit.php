<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserCredit extends Model
{
    protected $fillable = [
        'user_id',
        'credits',
        'used_credits',
        'notes',
    ];

    /**
     * Get the user that owns the credit
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get available credits
     */
    public function getAvailableCredits(): int
    {
        return max(0, $this->credits - $this->used_credits);
    }

    /**
     * Add credits
     */
    public function addCredits(int $amount, string $notes = null): void
    {
        $this->increment('credits', $amount);
        if ($notes) {
            $this->update(['notes' => $notes]);
        }
    }

    /**
     * Use credits
     */
    public function useCredits(int $amount): bool
    {
        if ($this->getAvailableCredits() >= $amount) {
            $this->increment('used_credits', $amount);
            return true;
        }
        return false;
    }
}
