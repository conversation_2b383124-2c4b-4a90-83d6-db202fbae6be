<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class IeltsAttempt extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'task_type',
        'essay_content',
        'task_achievement',
        'coherence_cohesion',
        'lexical_resource',
        'grammatical_accuracy',
        'overall_score',
        'feedback',
        'status'
    ];

    protected $casts = [
        'task_achievement' => 'decimal:1',
        'coherence_cohesion' => 'decimal:1',
        'lexical_resource' => 'decimal:1',
        'grammatical_accuracy' => 'decimal:1',
        'overall_score' => 'decimal:1',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function getStatusBadgeAttribute()
    {
        switch ($this->status) {
            case 'completed':
                return '<span class="badge bg-success">Hoàn thành</span>';
            case 'processing':
                return '<span class="badge bg-warning"><PERSON>ang xử lý</span>';
            case 'pending':
                return '<span class="badge bg-secondary">Chờ xử lý</span>';
            default:
                return '<span class="badge bg-light"><PERSON>h<PERSON>ng xác đ<PERSON>nh</span>';
        }
    }

    public function getTaskTypeNameAttribute()
    {
        return $this->task_type === 'task1' ? 'Task 1' : 'Task 2';
    }
}
