<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\UserCredit;
use Illuminate\Support\Facades\Hash;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $users = User::with('credit')->paginate(20);
        return view('admin.users.index', compact('users'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.users.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|in:user,admin',
            'credits' => 'required|integer|min:0',
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $request->role,
            'is_active' => true,
        ]);

        UserCredit::create([
            'user_id' => $user->id,
            'credits' => $request->credits,
            'used_credits' => 0,
            'notes' => 'Initial credits from admin',
        ]);

        return redirect()->route('admin.users.index')
            ->with('success', 'Người dùng đã được tạo thành công.');
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user)
    {
        $user->load(['credit', 'scoringAttempts.essayQuestion']);
        return view('admin.users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user)
    {
        return view('admin.users.edit', compact('user'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $user)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'role' => 'required|in:user,admin',
            'is_active' => 'boolean',
        ]);

        $user->update([
            'name' => $request->name,
            'email' => $request->email,
            'role' => $request->role,
            'is_active' => $request->has('is_active'),
        ]);

        return redirect()->route('admin.users.index')
            ->with('success', 'Thông tin người dùng đã được cập nhật.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user)
    {
        if ($user->id === auth()->id()) {
            return redirect()->route('admin.users.index')
                ->with('error', 'Bạn không thể xóa tài khoản của chính mình.');
        }

        $user->delete();

        return redirect()->route('admin.users.index')
            ->with('success', 'Người dùng đã được xóa.');
    }

    /**
     * Add credits to user
     */
    public function addCredits(Request $request, User $user)
    {
        try {
            $request->validate([
                'credits' => 'required|integer|min:1',
                'notes' => 'nullable|string|max:255',
            ]);

            $credit = $user->credit;
            if (!$credit) {
                $credit = UserCredit::create([
                    'user_id' => $user->id,
                    'credits' => 0,
                    'used_credits' => 0,
                    'notes' => 'Initial credit record',
                ]);
            }

            // Add credits using the model method
            $credit->addCredits($request->credits, $request->notes);

            return redirect()->back()
                ->with('success', "Đã thêm {$request->credits} credits cho {$user->name}.");

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', "Lỗi khi thêm credits: " . $e->getMessage());
        }
    }
}
