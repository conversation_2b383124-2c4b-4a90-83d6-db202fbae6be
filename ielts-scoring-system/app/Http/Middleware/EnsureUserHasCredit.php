<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class EnsureUserHasCredit
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (auth()->check()) {
            $user = auth()->user();

            // Ensure user has credit record
            if (!$user->credit) {
                \App\Models\UserCredit::create([
                    'user_id' => $user->id,
                    'credits' => 0,
                    'used_credits' => 0,
                    'notes' => 'Auto-created credit record',
                ]);
            }
        }

        return $next($request);
    }
}
