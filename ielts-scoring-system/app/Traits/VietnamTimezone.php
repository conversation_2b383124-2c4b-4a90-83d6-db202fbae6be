<?php

namespace App\Traits;

use Carbon\Carbon;
use App\Helpers\DateTimeHelper;

trait VietnamTimezone
{
    /**
     * Get created_at in Vietnam timezone
     */
    public function getCreatedAtVnAttribute()
    {
        return DateTimeHelper::toVietnamTime($this->created_at);
    }
    
    /**
     * Get updated_at in Vietnam timezone
     */
    public function getUpdatedAtVnAttribute()
    {
        return DateTimeHelper::toVietnamTime($this->updated_at);
    }
    
    /**
     * Get created_at date only
     */
    public function getCreatedDateVnAttribute()
    {
        return DateTimeHelper::toVietnamDate($this->created_at);
    }
    
    /**
     * Get created_at time only
     */
    public function getCreatedTimeVnAttribute()
    {
        return DateTimeHelper::toVietnamTimeOnly($this->created_at);
    }
    
    /**
     * Get time ago for created_at
     */
    public function getCreatedTimeAgoAttribute()
    {
        return DateTimeHelper::timeAgo($this->created_at);
    }
    
    /**
     * Get time ago for updated_at
     */
    public function getUpdatedTimeAgoAttribute()
    {
        return DateTimeHelper::timeAgo($this->updated_at);
    }
    
    /**
     * Format any datetime attribute to Vietnam timezone
     */
    public function formatVnTime($attribute, $format = 'd/m/Y H:i')
    {
        if (!$this->$attribute) {
            return null;
        }
        
        return DateTimeHelper::toVietnamTime($this->$attribute, $format);
    }
}
