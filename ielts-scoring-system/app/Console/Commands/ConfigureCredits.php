<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class ConfigureCredits extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'credits:configure {credits? : Number of default credits for new users}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Configure default credits for new user registration';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $credits = $this->argument('credits');

        if ($credits === null) {
            $credits = $this->ask('How many credits should new users receive?', config('credits.default_credits', 5));
        }

        // Validate input
        if (!is_numeric($credits) || $credits < 0) {
            $this->error('Credits must be a non-negative number.');
            return 1;
        }

        $credits = (int) $credits;

        // Update .env file
        $this->updateEnvFile('DEFAULT_USER_CREDITS', $credits);

        // Clear config cache
        $this->call('config:clear');

        if ($credits === 0) {
            $this->info('✅ New users will receive NO credits upon registration.');
        } else {
            $this->info("✅ New users will receive {$credits} credits upon registration.");
        }

        $this->line('');
        $this->line('Current credit configuration:');
        $this->table(
            ['Setting', 'Value'],
            [
                ['Default Credits', $credits],
                ['IELTS Scoring Cost', config('credits.costs.ielts_scoring', 1) . ' credit(s)'],
                ['Detailed Feedback Cost', config('credits.costs.detailed_feedback', 2) . ' credit(s)'],
                ['Premium Analysis Cost', config('credits.costs.premium_analysis', 3) . ' credit(s)'],
            ]
        );

        return 0;
    }

    /**
     * Update .env file with new value
     */
    private function updateEnvFile($key, $value)
    {
        $envFile = base_path('.env');

        if (!File::exists($envFile)) {
            $this->error('.env file not found.');
            return;
        }

        $envContent = File::get($envFile);

        // Check if key exists
        if (preg_match("/^{$key}=.*$/m", $envContent)) {
            // Update existing key
            $envContent = preg_replace("/^{$key}=.*$/m", "{$key}={$value}", $envContent);
        } else {
            // Add new key
            $envContent .= "\n{$key}={$value}";
        }

        File::put($envFile, $envContent);
    }
}
