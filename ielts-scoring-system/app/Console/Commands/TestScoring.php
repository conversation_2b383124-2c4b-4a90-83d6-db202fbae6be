<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\IELTSScorer;

class TestScoring extends Command
{
    protected $signature = 'test:scoring';
    protected $description = 'Test IELTS scoring functionality';

    public function handle()
    {
        $this->info('Testing IELTS Scoring...');

        $scorer = new IELTSScorer();
        
        $essay = 'Technology has revolutionized education in many ways. Online learning platforms provide access to courses from top universities worldwide. However, some argue that technology makes students lazy and reduces critical thinking skills. In my opinion, technology is a valuable tool when used properly in educational settings.';
        
        $taskType = 'task2';
        $question = 'Discuss the advantages and disadvantages of technology in education.';
        $timeLimit = 40;

        try {
            $this->info('Starting scoring...');
            
            $result = $scorer->scoreEssayComprehensive($essay, $taskType, $question, $timeLimit);
            
            $this->info('Scoring completed successfully!');
            $this->info('Overall Score: ' . ($result['overall_band_score'] ?? 'N/A'));
            $this->info('Task Achievement: ' . ($result['criteria_scores']['task_achievement'] ?? 'N/A'));
            
            return 0;
            
        } catch (\Exception $e) {
            $this->error('Scoring failed: ' . $e->getMessage());
            return 1;
        }
    }
}
