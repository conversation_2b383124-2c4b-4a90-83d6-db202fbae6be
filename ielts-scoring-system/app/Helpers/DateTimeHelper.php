<?php

namespace App\Helpers;

use Carbon\Carbon;

class DateTimeHelper
{
    /**
     * Format datetime to Vietnam timezone
     */
    public static function toVietnamTime($datetime, $format = 'd/m/Y H:i')
    {
        if (!$datetime) {
            return null;
        }
        
        return Carbon::parse($datetime)
            ->setTimezone('Asia/Ho_Chi_Minh')
            ->format($format);
    }
    
    /**
     * Format date only
     */
    public static function toVietnamDate($datetime, $format = 'd/m/Y')
    {
        if (!$datetime) {
            return null;
        }
        
        return Carbon::parse($datetime)
            ->setTimezone('Asia/Ho_Chi_Minh')
            ->format($format);
    }
    
    /**
     * Format time only
     */
    public static function toVietnamTimeOnly($datetime, $format = 'H:i')
    {
        if (!$datetime) {
            return null;
        }
        
        return Carbon::parse($datetime)
            ->setTimezone('Asia/Ho_Chi_Minh')
            ->format($format);
    }
    
    /**
     * Format datetime with day name
     */
    public static function toVietnamDateTimeWithDay($datetime, $format = 'l, d/m/Y H:i')
    {
        if (!$datetime) {
            return null;
        }
        
        $carbon = Carbon::parse($datetime)->setTimezone('Asia/Ho_Chi_Minh');
        
        // Vietnamese day names
        $dayNames = [
            'Monday' => 'Thứ Hai',
            'Tuesday' => 'Thứ Ba', 
            'Wednesday' => 'Thứ Tư',
            'Thursday' => 'Thứ Năm',
            'Friday' => 'Thứ Sáu',
            'Saturday' => 'Thứ Bảy',
            'Sunday' => 'Chủ Nhật'
        ];
        
        $englishDay = $carbon->format('l');
        $vietnameseDay = $dayNames[$englishDay] ?? $englishDay;
        
        return $vietnameseDay . ', ' . $carbon->format('d/m/Y H:i');
    }
    
    /**
     * Get relative time (time ago)
     */
    public static function timeAgo($datetime)
    {
        if (!$datetime) {
            return null;
        }
        
        $carbon = Carbon::parse($datetime)->setTimezone('Asia/Ho_Chi_Minh');
        
        // Set Vietnamese locale for diffForHumans
        $now = Carbon::now('Asia/Ho_Chi_Minh');
        $diff = $carbon->diffInMinutes($now);
        
        if ($diff < 1) {
            return 'Vừa xong';
        } elseif ($diff < 60) {
            return $diff . ' phút trước';
        } elseif ($diff < 1440) { // 24 hours
            $hours = floor($diff / 60);
            return $hours . ' giờ trước';
        } elseif ($diff < 10080) { // 7 days
            $days = floor($diff / 1440);
            return $days . ' ngày trước';
        } else {
            return self::toVietnamDate($datetime);
        }
    }
    
    /**
     * Get current Vietnam time
     */
    public static function now($format = 'd/m/Y H:i:s')
    {
        return Carbon::now('Asia/Ho_Chi_Minh')->format($format);
    }
    
    /**
     * Check if datetime is today
     */
    public static function isToday($datetime)
    {
        if (!$datetime) {
            return false;
        }
        
        $carbon = Carbon::parse($datetime)->setTimezone('Asia/Ho_Chi_Minh');
        $today = Carbon::today('Asia/Ho_Chi_Minh');
        
        return $carbon->isSameDay($today);
    }
    
    /**
     * Format for database storage (UTC)
     */
    public static function toUtc($datetime)
    {
        if (!$datetime) {
            return null;
        }
        
        return Carbon::parse($datetime, 'Asia/Ho_Chi_Minh')
            ->utc()
            ->format('Y-m-d H:i:s');
    }
}
