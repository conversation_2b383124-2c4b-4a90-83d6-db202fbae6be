<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\ScoringAttempt;

echo "=== Test Score Adjustment System ===\n\n";

// Create a test scoring attempt
$attempt = new ScoringAttempt();
$attempt->setRawAttributes([
    'overall_band_score' => 7.0,
    'task_achievement' => 6.5,
    'coherence_cohesion' => 7.5,
    'lexical_resource' => 6.0,
    'grammar_accuracy' => 7.0,
]);

echo "Original Scores:\n";
echo "- Overall Band Score: " . $attempt->getOriginalOverallBandScore() . "\n";
echo "- Task Achievement: " . $attempt->getOriginalTaskAchievement() . "\n";
echo "- Coherence & Cohesion: " . $attempt->getOriginalCoherenceCohesion() . "\n";
echo "- Lexical Resource: " . $attempt->getOriginalLexicalResource() . "\n";
echo "- Grammar Accuracy: " . $attempt->getOriginalGrammarAccuracy() . "\n\n";

echo "Adjusted Scores (with -0.5 adjustment):\n";
echo "- Overall Band Score: " . $attempt->overall_band_score . "\n";
echo "- Task Achievement: " . $attempt->task_achievement . "\n";
echo "- Coherence & Cohesion: " . $attempt->coherence_cohesion . "\n";
echo "- Lexical Resource: " . $attempt->lexical_resource . "\n";
echo "- Grammar Accuracy: " . $attempt->grammar_accuracy . "\n\n";

echo "Configuration Values:\n";
echo "- Overall adjustment: " . config('scoring.adjustments.overall_band_score') . "\n";
echo "- Task achievement adjustment: " . config('scoring.adjustments.task_achievement') . "\n";
echo "- Coherence cohesion adjustment: " . config('scoring.adjustments.coherence_cohesion') . "\n";
echo "- Lexical resource adjustment: " . config('scoring.adjustments.lexical_resource') . "\n";
echo "- Grammar accuracy adjustment: " . config('scoring.adjustments.grammar_accuracy') . "\n";
echo "- Enable rounding: " . (config('scoring.enable_rounding') ? 'true' : 'false') . "\n";
echo "- Minimum score: " . config('scoring.minimum_score') . "\n";
echo "- Maximum score: " . config('scoring.maximum_score') . "\n";

echo "\n=== Test Completed ===\n";
