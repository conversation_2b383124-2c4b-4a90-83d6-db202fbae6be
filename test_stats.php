<?php
// Test API to check statistics calculation

$testData = [
    'essay' => 'International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.',
    'task_type' => 'task2',
    'essay_question' => 'Some people think that secondary school students should study international news as a school subject, while others believe that this is a waste of their time. Discuss both views and give your own opinion.',
    'time_limit' => 40
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/ielts/api.php');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 60);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: " . $httpCode . "\n\n";

// Parse and check for statistics
$data = json_decode($response, true);
if ($data && isset($data['statistics'])) {
    echo "✅ Statistics found:\n";
    print_r($data['statistics']);
} else {
    echo "❌ Statistics not found in response\n";
    if ($data) {
        echo "Available keys: " . implode(', ', array_keys($data)) . "\n";
        
        // Show sample of response
        echo "\nSample response structure:\n";
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                echo "$key: [array with " . count($value) . " items]\n";
            } else {
                echo "$key: " . (is_string($value) ? substr($value, 0, 50) . "..." : $value) . "\n";
            }
        }
    }
}
?>
