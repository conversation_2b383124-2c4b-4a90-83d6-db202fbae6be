/* IELTS Essay Highlighter Styles */

.highlighted-essay-container {
    background: #fff;
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.essay-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f8f9fa;
}

.essay-header h4 {
    color: #333;
    margin-bottom: 15px;
}

/* Legend */
.legend {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 10px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
    color: #666;
}

.highlight-sample {
    width: 20px;
    height: 12px;
    border-radius: 3px;
    display: inline-block;
}

/* Essay Content */
.essay-content {
    background: #fafafa;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    line-height: 1.8;
    font-size: 1.1rem;
    margin-bottom: 20px;
    position: relative;
}

/* Highlight Styles */
.highlight {
    position: relative;
    cursor: pointer;
    border-radius: 3px;
    padding: 2px 4px;
    margin: 0 1px;
    transition: all 0.3s ease;
    display: inline-block;
}

.highlight:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

/* Error Type Colors */
.highlight.grammar {
    background-color: rgba(220, 53, 69, 0.2);
    border-bottom: 2px wavy #dc3545;
}

.highlight.vocabulary {
    background-color: rgba(255, 193, 7, 0.2);
    border-bottom: 2px wavy #ffc107;
}

.highlight.coherence {
    background-color: rgba(13, 202, 240, 0.2);
    border-bottom: 2px wavy #0dcaf0;
}

.highlight.task_response {
    background-color: rgba(111, 66, 193, 0.2);
    border-bottom: 2px wavy #6f42c1;
}

/* Severity Levels */
.highlight.high {
    font-weight: bold;
    border-width: 3px;
}

.highlight.medium {
    border-width: 2px;
}

.highlight.low {
    border-width: 1px;
    opacity: 0.8;
}

/* Correction Icon */
.correction-icon {
    font-size: 0.8rem;
    margin-left: 3px;
    opacity: 0.7;
}

.highlight.grammar .correction-icon {
    color: #dc3545;
}

.highlight.vocabulary .correction-icon {
    color: #ffc107;
}

.highlight.coherence .correction-icon {
    color: #0dcaf0;
}

.highlight.task_response .correction-icon {
    color: #6f42c1;
}

/* Quick Tooltip */
.quick-tooltip {
    position: absolute;
    background: #333;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.9rem;
    max-width: 300px;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    pointer-events: none;
}

.quick-tooltip::before {
    content: '';
    position: absolute;
    top: -5px;
    left: 20px;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-bottom: 5px solid #333;
}

/* Correction Modal */
.correction-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.correction-modal.show {
    opacity: 1;
}

.modal-content {
    background: white;
    border-radius: 15px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    transform: translateY(-20px);
    transition: transform 0.3s ease;
}

.correction-modal.show .modal-content {
    transform: translateY(0);
}

.modal-header {
    padding: 20px 25px 15px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: between;
    align-items: center;
}

.modal-header h5 {
    margin: 0;
    color: #333;
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-modal:hover {
    color: #333;
}

.modal-body {
    padding: 20px 25px;
}

.correction-field {
    margin-bottom: 15px;
}

.correction-field label {
    font-weight: bold;
    color: #555;
    display: block;
    margin-bottom: 5px;
}

.original-text {
    background: #ffe6e6;
    padding: 10px;
    border-radius: 5px;
    border-left: 4px solid #dc3545;
    font-style: italic;
}

.suggested-text {
    background: #e6ffe6;
    padding: 10px;
    border-radius: 5px;
    border-left: 4px solid #28a745;
    font-weight: 500;
}

.explanation-text {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    border-left: 4px solid #6c757d;
    color: #555;
}

.correction-meta {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.error-type, .severity {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
}

.error-type.grammar {
    background: #dc3545;
    color: white;
}

.error-type.vocabulary {
    background: #ffc107;
    color: #333;
}

.error-type.coherence {
    background: #0dcaf0;
    color: white;
}

.error-type.task_response {
    background: #6f42c1;
    color: white;
}

.severity.high {
    background: #dc3545;
    color: white;
}

.severity.medium {
    background: #ffc107;
    color: #333;
}

.severity.low {
    background: #28a745;
    color: white;
}

.modal-footer {
    padding: 15px 25px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* Corrections Summary */
.corrections-summary {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
}

.corrections-summary h5 {
    color: #333;
    margin-bottom: 15px;
}

.correction-type-group {
    margin-bottom: 20px;
}

.correction-type-group h6 {
    margin-bottom: 10px;
    padding: 8px 12px;
    border-radius: 5px;
    color: white;
}

.correction-type-group h6.grammar {
    background: #dc3545;
}

.correction-type-group h6.vocabulary {
    background: #ffc107;
    color: #333;
}

.correction-type-group h6.coherence {
    background: #0dcaf0;
}

.correction-type-group h6.task_response {
    background: #6f42c1;
}

.corrections-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.correction-item {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    border-left: 4px solid #ddd;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.correction-item.high {
    border-left-color: #dc3545;
}

.correction-item.medium {
    border-left-color: #ffc107;
}

.correction-item.low {
    border-left-color: #28a745;
}

.correction-item strong {
    color: #333;
}

.correction-item .explanation {
    color: #666;
    font-style: italic;
    margin-top: 5px;
    display: block;
}

/* Responsive Design */
@media (max-width: 768px) {
    .highlighted-essay-container {
        padding: 15px;
        margin: 10px 0;
    }
    
    .legend {
        flex-direction: column;
        gap: 8px;
    }
    
    .essay-content {
        padding: 15px;
        font-size: 1rem;
    }
    
    .modal-content {
        width: 95%;
        margin: 10px;
    }
    
    .modal-header, .modal-body, .modal-footer {
        padding: 15px 20px;
    }
    
    .modal-footer {
        flex-direction: column;
    }
}
