/**
 * IELTS Progress Tracker
 * Tracks student progress and provides learning insights
 */

class IELTSProgressTracker {
    constructor() {
        this.storageKey = 'ielts_progress_data';
        this.progressData = this.loadProgressData();
    }

    /**
     * Load progress data from localStorage
     */
    loadProgressData() {
        const saved = localStorage.getItem(this.storageKey);
        if (saved) {
            try {
                return JSON.parse(saved);
            } catch (e) {
                console.error('Error loading progress data:', e);
            }
        }
        
        return {
            sessions: [],
            totalEssays: 0,
            averageScore: 0,
            improvementTrend: [],
            commonErrors: {},
            studyTime: 0
        };
    }

    /**
     * Save progress data to localStorage
     */
    saveProgressData() {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(this.progressData));
        } catch (e) {
            console.error('Error saving progress data:', e);
        }
    }

    /**
     * Add a new scoring session
     */
    addSession(scoringResult, originalEssay, taskType) {
        const session = {
            id: Date.now(),
            date: new Date().toISOString(),
            taskType: taskType,
            overallScore: scoringResult.overall_band_score || 0,
            criteriaScores: scoringResult.criteria_scores || {},
            wordCount: scoringResult.metadata?.word_count || 0,
            corrections: scoringResult.highlighted_corrections || [],
            essayPreview: originalEssay.substring(0, 100) + '...'
        };

        this.progressData.sessions.unshift(session); // Add to beginning
        this.progressData.totalEssays++;
        
        // Keep only last 50 sessions
        if (this.progressData.sessions.length > 50) {
            this.progressData.sessions = this.progressData.sessions.slice(0, 50);
        }

        this.updateStatistics();
        this.saveProgressData();
        
        return session;
    }

    /**
     * Update overall statistics
     */
    updateStatistics() {
        const sessions = this.progressData.sessions;
        if (sessions.length === 0) return;

        // Calculate average score
        const totalScore = sessions.reduce((sum, session) => sum + session.overallScore, 0);
        this.progressData.averageScore = totalScore / sessions.length;

        // Update improvement trend (last 10 sessions)
        const recentSessions = sessions.slice(0, 10).reverse();
        this.progressData.improvementTrend = recentSessions.map(session => ({
            date: session.date,
            score: session.overallScore
        }));

        // Analyze common errors
        this.analyzeCommonErrors();
    }

    /**
     * Analyze common error patterns
     */
    analyzeCommonErrors() {
        const errorCounts = {};
        
        this.progressData.sessions.forEach(session => {
            session.corrections.forEach(correction => {
                const errorType = correction.error_type;
                if (!errorCounts[errorType]) {
                    errorCounts[errorType] = {
                        count: 0,
                        examples: []
                    };
                }
                errorCounts[errorType].count++;
                
                // Keep up to 3 examples per error type
                if (errorCounts[errorType].examples.length < 3) {
                    errorCounts[errorType].examples.push({
                        original: correction.original_text,
                        corrected: correction.suggested_correction
                    });
                }
            });
        });

        this.progressData.commonErrors = errorCounts;
    }

    /**
     * Get progress summary
     */
    getProgressSummary() {
        const sessions = this.progressData.sessions;
        if (sessions.length === 0) {
            return {
                totalEssays: 0,
                averageScore: 0,
                improvement: 0,
                strongestSkill: 'N/A',
                weakestSkill: 'N/A',
                recentTrend: 'No data'
            };
        }

        // Calculate improvement (compare first 5 vs last 5 sessions)
        let improvement = 0;
        if (sessions.length >= 10) {
            const recent5 = sessions.slice(0, 5);
            const older5 = sessions.slice(-5);
            
            const recentAvg = recent5.reduce((sum, s) => sum + s.overallScore, 0) / 5;
            const olderAvg = older5.reduce((sum, s) => sum + s.overallScore, 0) / 5;
            
            improvement = recentAvg - olderAvg;
        }

        // Find strongest and weakest skills
        const criteriaAverages = this.calculateCriteriaAverages();
        const sortedCriteria = Object.entries(criteriaAverages)
            .sort((a, b) => b[1] - a[1]);
        
        const strongestSkill = sortedCriteria[0] ? this.formatCriteriaName(sortedCriteria[0][0]) : 'N/A';
        const weakestSkill = sortedCriteria[sortedCriteria.length - 1] ? 
            this.formatCriteriaName(sortedCriteria[sortedCriteria.length - 1][0]) : 'N/A';

        // Determine recent trend
        let recentTrend = 'Stable';
        if (sessions.length >= 3) {
            const last3 = sessions.slice(0, 3);
            const scores = last3.map(s => s.overallScore);
            
            if (scores[0] > scores[1] && scores[1] > scores[2]) {
                recentTrend = 'Improving';
            } else if (scores[0] < scores[1] && scores[1] < scores[2]) {
                recentTrend = 'Declining';
            }
        }

        return {
            totalEssays: this.progressData.totalEssays,
            averageScore: this.progressData.averageScore,
            improvement: improvement,
            strongestSkill: strongestSkill,
            weakestSkill: weakestSkill,
            recentTrend: recentTrend
        };
    }

    /**
     * Calculate average scores for each criteria
     */
    calculateCriteriaAverages() {
        const criteriaData = {};
        const sessions = this.progressData.sessions;

        sessions.forEach(session => {
            Object.entries(session.criteriaScores).forEach(([criteria, data]) => {
                if (!criteriaData[criteria]) {
                    criteriaData[criteria] = [];
                }
                criteriaData[criteria].push(data.score || 0);
            });
        });

        const averages = {};
        Object.entries(criteriaData).forEach(([criteria, scores]) => {
            averages[criteria] = scores.reduce((sum, score) => sum + score, 0) / scores.length;
        });

        return averages;
    }

    /**
     * Format criteria names for display
     */
    formatCriteriaName(criteria) {
        const names = {
            'task_achievement': 'Task Achievement',
            'coherence_cohesion': 'Coherence & Cohesion',
            'lexical_resource': 'Lexical Resource',
            'grammatical_range': 'Grammatical Range'
        };
        return names[criteria] || criteria;
    }

    /**
     * Get learning recommendations
     */
    getLearningRecommendations() {
        const summary = this.getProgressSummary();
        const commonErrors = this.progressData.commonErrors;
        const recommendations = [];

        // Score-based recommendations
        if (summary.averageScore < 6.0) {
            recommendations.push({
                type: 'urgent',
                title: 'Focus on Fundamentals',
                description: 'Your current average score suggests focusing on basic writing skills and task understanding.',
                actions: ['Practice basic sentence structures', 'Study IELTS task requirements', 'Build core vocabulary']
            });
        } else if (summary.averageScore < 7.0) {
            recommendations.push({
                type: 'important',
                title: 'Develop Advanced Skills',
                description: 'Work on more sophisticated language use and complex ideas.',
                actions: ['Practice complex sentences', 'Expand academic vocabulary', 'Improve argument development']
            });
        }

        // Error-based recommendations
        const errorTypes = Object.keys(commonErrors);
        if (errorTypes.length > 0) {
            const mostCommonError = errorTypes.reduce((a, b) => 
                commonErrors[a].count > commonErrors[b].count ? a : b
            );

            const errorRecommendations = {
                'grammar': {
                    title: 'Grammar Focus Needed',
                    actions: ['Review tense usage', 'Practice subject-verb agreement', 'Study sentence structures']
                },
                'vocabulary': {
                    title: 'Vocabulary Enhancement',
                    actions: ['Learn academic word lists', 'Practice word formation', 'Study collocations']
                },
                'coherence': {
                    title: 'Improve Organization',
                    actions: ['Practice paragraph structure', 'Learn linking words', 'Study essay organization']
                },
                'task_response': {
                    title: 'Task Understanding',
                    actions: ['Analyze task requirements', 'Practice different question types', 'Improve idea development']
                }
            };

            if (errorRecommendations[mostCommonError]) {
                recommendations.push({
                    type: 'focus',
                    title: errorRecommendations[mostCommonError].title,
                    description: `You have ${commonErrors[mostCommonError].count} ${mostCommonError} errors in recent essays.`,
                    actions: errorRecommendations[mostCommonError].actions
                });
            }
        }

        // Trend-based recommendations
        if (summary.recentTrend === 'Declining') {
            recommendations.push({
                type: 'warning',
                title: 'Address Recent Decline',
                description: 'Your recent scores show a declining trend. Consider reviewing fundamentals.',
                actions: ['Review recent mistakes', 'Practice more regularly', 'Seek additional feedback']
            });
        }

        return recommendations;
    }

    /**
     * Export progress data
     */
    exportData() {
        const data = {
            exportDate: new Date().toISOString(),
            progressData: this.progressData,
            summary: this.getProgressSummary(),
            recommendations: this.getLearningRecommendations()
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `ielts-progress-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    /**
     * Clear all progress data
     */
    clearData() {
        if (confirm('Are you sure you want to clear all progress data? This cannot be undone.')) {
            localStorage.removeItem(this.storageKey);
            this.progressData = {
                sessions: [],
                totalEssays: 0,
                averageScore: 0,
                improvementTrend: [],
                commonErrors: {},
                studyTime: 0
            };
            return true;
        }
        return false;
    }
}

// Export for use
window.IELTSProgressTracker = IELTSProgressTracker;
