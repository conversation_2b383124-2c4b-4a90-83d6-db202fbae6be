/**
 * IELTS Essay Highlighter and Correction Display
 * Provides visual feedback like a teacher marking papers
 */

class EssayHighlighter {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.corrections = [];
        this.originalEssay = '';
        this.annotatedEssay = '';
    }

    /**
     * Initialize the highlighter with scoring results
     */
    init(scoringResult, originalEssay) {
        console.log('Initializing highlighter with:', scoringResult);

        this.originalEssay = originalEssay;
        this.corrections = scoringResult.highlighted_corrections || [];
        this.annotatedEssay = scoringResult.annotated_essay || originalEssay;

        // Debug: Log corrections data
        console.log('Corrections found:', this.corrections);
        this.corrections.forEach((correction, index) => {
            console.log(`Correction ${index}:`, {
                original: correction.original_text,
                suggestion: correction.suggested_correction,
                type: correction.error_type,
                severity: correction.severity
            });
        });

        // Try to render, with fallback if it fails
        try {
            this.renderHighlightedEssay();
            this.setupInteractions();
        } catch (error) {
            console.error('Error rendering highlighted essay:', error);
            this.renderFallbackEssay();
        }
    }

    /**
     * Render the essay with highlights and corrections
     */
    renderHighlightedEssay() {
        if (!this.container) return;

        // Create the highlighted essay container
        const essayContainer = document.createElement('div');
        essayContainer.className = 'highlighted-essay-container';
        essayContainer.innerHTML = `
            <div class="essay-header">
                <h4><i class="fas fa-edit"></i> Your Essay with Teacher's Corrections</h4>
                <div class="legend">
                    <span class="legend-item">
                        <span class="highlight-sample grammar"></span> Grammar
                    </span>
                    <span class="legend-item">
                        <span class="highlight-sample vocabulary"></span> Vocabulary
                    </span>
                    <span class="legend-item">
                        <span class="highlight-sample coherence"></span> Coherence
                    </span>
                    <span class="legend-item">
                        <span class="highlight-sample task_response"></span> Task Response
                    </span>
                </div>
            </div>
            <div class="essay-content" id="essayContent"></div>
            <div class="corrections-summary" id="correctionsSummary"></div>
        `;

        this.container.appendChild(essayContainer);
        
        this.renderEssayContent();
        this.renderCorrectionsSummary();
    }

    /**
     * Render the essay content with highlights
     */
    renderEssayContent() {
        const contentDiv = document.getElementById('essayContent');
        if (!contentDiv) return;

        // Start with clean original essay
        let processedEssay = this.originalEssay;

        // If no corrections, just display the essay
        if (!this.corrections || this.corrections.length === 0) {
            contentDiv.innerHTML = processedEssay.replace(/\n/g, '<br>');
            return;
        }

        // Sort corrections by position (longest first to avoid overlap issues)
        const sortedCorrections = [...this.corrections].sort((a, b) =>
            (b.original_text?.length || 0) - (a.original_text?.length || 0)
        );

        // Apply highlights to each correction
        sortedCorrections.forEach((correction, index) => {
            if (!correction.original_text) return; // Skip if no original text

            const correctionId = `correction-${index}`;
            const highlightClass = `highlight ${correction.error_type || 'grammar'} ${correction.severity || 'medium'}`;

            // Clean the correction texts
            const originalText = this.cleanText(correction.original_text);
            const suggestedText = this.cleanText(correction.suggested_correction || '');
            const explanationText = this.cleanText(correction.explanation || '');

            console.log(`Processing correction ${index}:`, {
                original: originalText,
                suggestion: suggestedText,
                found: processedEssay.toLowerCase().includes(originalText.toLowerCase())
            });

            // Only proceed if we can find the text in the essay (case insensitive)
            if (originalText && processedEssay.toLowerCase().includes(originalText.toLowerCase())) {
                // Create highlighted span with tooltip
                const highlightedText = `<span class="${highlightClass}"
                    data-correction-id="${correctionId}"
                    data-original="${this.escapeHtml(originalText)}"
                    data-suggestion="${this.escapeHtml(suggestedText)}"
                    data-explanation="${this.escapeHtml(explanationText)}"
                    data-type="${correction.error_type || 'grammar'}"
                    data-severity="${correction.severity || 'medium'}"
                    title="Click for suggestion">
                    ${this.escapeHtml(originalText)}
                    <i class="fas fa-exclamation-circle correction-icon"></i>
                </span>`;

                // Replace the first occurrence only to avoid multiple replacements (case insensitive)
                const regex = new RegExp(this.escapeRegex(originalText), 'i');
                processedEssay = processedEssay.replace(regex, highlightedText);
            }
        });

        // Convert line breaks to HTML
        processedEssay = processedEssay.replace(/\n/g, '<br>');

        contentDiv.innerHTML = processedEssay;
    }

    /**
     * Render corrections summary
     */
    renderCorrectionsSummary() {
        const summaryDiv = document.getElementById('correctionsSummary');
        if (!summaryDiv || this.corrections.length === 0) return;

        const correctionsByType = this.groupCorrectionsByType();
        
        let summaryHTML = '<h5><i class="fas fa-list"></i> Corrections Summary</h5>';
        
        Object.keys(correctionsByType).forEach(type => {
            const corrections = correctionsByType[type];
            const typeLabel = this.getTypeLabel(type);
            
            summaryHTML += `
                <div class="correction-type-group">
                    <h6 class="${type}">${typeLabel} (${corrections.length})</h6>
                    <ul class="corrections-list">
            `;
            
            corrections.forEach((correction, index) => {
                summaryHTML += `
                    <li class="correction-item ${correction.severity}">
                        <strong>Original:</strong> "${correction.original_text}"<br>
                        <strong>Suggested:</strong> "${correction.suggested_correction}"<br>
                        <small class="explanation">${correction.explanation}</small>
                    </li>
                `;
            });
            
            summaryHTML += '</ul></div>';
        });

        summaryDiv.innerHTML = summaryHTML;
    }

    /**
     * Setup interactive features
     */
    setupInteractions() {
        // Click handlers for highlighted text
        document.addEventListener('click', (e) => {
            if (e.target.closest('.highlight')) {
                this.showCorrectionModal(e.target.closest('.highlight'));
            }
        });

        // Hover effects
        document.addEventListener('mouseover', (e) => {
            if (e.target.closest('.highlight')) {
                this.showQuickTooltip(e.target.closest('.highlight'), e);
            }
        });

        document.addEventListener('mouseout', (e) => {
            if (e.target.closest('.highlight')) {
                this.hideQuickTooltip();
            }
        });
    }

    /**
     * Show correction modal
     */
    showCorrectionModal(element) {
        const modal = this.createCorrectionModal(element);
        document.body.appendChild(modal);
        
        // Show modal
        setTimeout(() => modal.classList.add('show'), 10);
        
        // Close handlers
        modal.querySelector('.close-modal').onclick = () => this.closeCorrectionModal(modal);
        modal.onclick = (e) => {
            if (e.target === modal) this.closeCorrectionModal(modal);
        };
    }

    /**
     * Create correction modal
     */
    createCorrectionModal(element) {
        const modal = document.createElement('div');
        modal.className = 'correction-modal';
        
        const original = element.dataset.original;
        const suggestion = element.dataset.suggestion;
        const explanation = element.dataset.explanation;
        const type = element.dataset.type;
        const severity = element.dataset.severity;
        
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h5><i class="fas fa-edit"></i> Correction Suggestion</h5>
                    <button class="close-modal">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="correction-details">
                        <div class="correction-field">
                            <label>Original Text:</label>
                            <div class="original-text">${original}</div>
                        </div>
                        <div class="correction-field">
                            <label>Suggested Correction:</label>
                            <div class="suggested-text">${suggestion}</div>
                        </div>
                        <div class="correction-field">
                            <label>Explanation:</label>
                            <div class="explanation-text">${explanation}</div>
                        </div>
                        <div class="correction-meta">
                            <span class="error-type ${type}">${this.getTypeLabel(type)}</span>
                            <span class="severity ${severity}">${severity.toUpperCase()}</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary copy-suggestion" data-text="${suggestion}">
                        <i class="fas fa-copy"></i> Copy Suggestion
                    </button>
                    <button class="btn btn-secondary close-modal">Close</button>
                </div>
            </div>
        `;

        // Copy functionality
        modal.querySelector('.copy-suggestion').onclick = (e) => {
            navigator.clipboard.writeText(e.target.dataset.text);
            e.target.innerHTML = '<i class="fas fa-check"></i> Copied!';
            setTimeout(() => {
                e.target.innerHTML = '<i class="fas fa-copy"></i> Copy Suggestion';
            }, 2000);
        };

        return modal;
    }

    /**
     * Close correction modal
     */
    closeCorrectionModal(modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            if (modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }
        }, 300);
    }

    /**
     * Show quick tooltip on hover
     */
    showQuickTooltip(element, event) {
        this.hideQuickTooltip(); // Remove any existing tooltip
        
        const tooltip = document.createElement('div');
        tooltip.className = 'quick-tooltip';
        tooltip.innerHTML = `
            <strong>${element.dataset.suggestion}</strong><br>
            <small>${element.dataset.explanation}</small>
        `;
        
        document.body.appendChild(tooltip);
        
        // Position tooltip
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + 'px';
        tooltip.style.top = (rect.bottom + 5) + 'px';
        
        this.currentTooltip = tooltip;
    }

    /**
     * Hide quick tooltip
     */
    hideQuickTooltip() {
        if (this.currentTooltip) {
            this.currentTooltip.remove();
            this.currentTooltip = null;
        }
    }

    /**
     * Fallback rendering if main highlighting fails
     */
    renderFallbackEssay() {
        if (!this.container) return;

        const fallbackContainer = document.createElement('div');
        fallbackContainer.className = 'highlighted-essay-container';
        fallbackContainer.innerHTML = `
            <div class="essay-header">
                <h4><i class="fas fa-exclamation-triangle text-warning"></i> Essay Display (Fallback Mode)</h4>
                <p class="text-muted">Highlighting temporarily unavailable. Showing corrections summary below.</p>
            </div>
            <div class="essay-content">
                ${this.originalEssay.replace(/\n/g, '<br>')}
            </div>
            <div class="corrections-summary" id="correctionsSummary"></div>
        `;

        this.container.appendChild(fallbackContainer);
        this.renderCorrectionsSummary();
    }

    /**
     * Helper methods
     */
    groupCorrectionsByType() {
        const groups = {};
        this.corrections.forEach(correction => {
            if (!groups[correction.error_type]) {
                groups[correction.error_type] = [];
            }
            groups[correction.error_type].push(correction);
        });
        return groups;
    }

    getTypeLabel(type) {
        const labels = {
            'grammar': 'Grammar',
            'vocabulary': 'Vocabulary',
            'coherence': 'Coherence & Cohesion',
            'task_response': 'Task Response'
        };
        return labels[type] || type;
    }

    cleanText(text) {
        if (!text) return '';

        // If it's already clean text (no HTML), return as is
        if (!text.includes('<') && !text.includes('&')) {
            return text.trim();
        }

        // Remove HTML tags and decode HTML entities
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = text;
        let cleanedText = tempDiv.textContent || tempDiv.innerText || '';

        // Remove extra whitespace and normalize
        cleanedText = cleanedText.replace(/\s+/g, ' ').trim();

        return cleanedText;
    }

    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    escapeRegex(string) {
        if (!string) return '';
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }
}

// Export for use
window.EssayHighlighter = EssayHighlighter;
