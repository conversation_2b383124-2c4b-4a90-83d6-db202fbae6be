<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserCredit;

class CreditService
{
    /**
     * Get default credits for new users
     */
    public static function getDefaultCredits(): int
    {
        return config('credits.default_credits', 5);
    }

    /**
     * Get credit cost for an action
     */
    public static function getCost(string $action): int
    {
        return config("credits.costs.{$action}", 1);
    }

    /**
     * Create initial credits for a new user
     */
    public static function createInitialCredits(User $user): ?UserCredit
    {
        $defaultCredits = self::getDefaultCredits();
        
        if ($defaultCredits <= 0) {
            return null;
        }

        return UserCredit::create([
            'user_id' => $user->id,
            'credits' => $defaultCredits,
            'used_credits' => 0,
            'notes' => "Welcome credits for new user ({$defaultCredits} credits)",
        ]);
    }

    /**
     * Check if user has enough credits for an action
     */
    public static function hasEnoughCredits(User $user, string $action): bool
    {
        $cost = self::getCost($action);
        $availableCredits = $user->getAvailableCredits();
        
        return $availableCredits >= $cost;
    }

    /**
     * Deduct credits from user for an action
     */
    public static function deductCredits(User $user, string $action, string $notes = ''): bool
    {
        $cost = self::getCost($action);
        
        if (!self::hasEnoughCredits($user, $action)) {
            return false;
        }

        $userCredit = $user->credit;
        if (!$userCredit) {
            return false;
        }

        $userCredit->increment('used_credits', $cost);
        
        // Log the transaction if needed
        if ($notes) {
            $userCredit->update([
                'notes' => $userCredit->notes . "\n" . now()->format('Y-m-d H:i:s') . ": {$notes} (-{$cost} credits)"
            ]);
        }

        return true;
    }

    /**
     * Add credits to user
     */
    public static function addCredits(User $user, int $amount, string $notes = ''): bool
    {
        $userCredit = $user->credit;
        
        if (!$userCredit) {
            // Create credit record if doesn't exist
            $userCredit = UserCredit::create([
                'user_id' => $user->id,
                'credits' => $amount,
                'used_credits' => 0,
                'notes' => $notes ?: "Credits added: {$amount}",
            ]);
        } else {
            $userCredit->increment('credits', $amount);
            
            if ($notes) {
                $userCredit->update([
                    'notes' => $userCredit->notes . "\n" . now()->format('Y-m-d H:i:s') . ": {$notes} (+{$amount} credits)"
                ]);
            }
        }

        return true;
    }

    /**
     * Get available credit packages
     */
    public static function getPackages(): array
    {
        return config('credits.packages', []);
    }

    /**
     * Get package by key
     */
    public static function getPackage(string $key): ?array
    {
        $packages = self::getPackages();
        return $packages[$key] ?? null;
    }

    /**
     * Purchase credits package
     */
    public static function purchasePackage(User $user, string $packageKey, string $transactionId = ''): bool
    {
        $package = self::getPackage($packageKey);
        
        if (!$package) {
            return false;
        }

        $notes = "Purchased {$package['name']} package";
        if ($transactionId) {
            $notes .= " (Transaction: {$transactionId})";
        }

        return self::addCredits($user, $package['credits'], $notes);
    }

    /**
     * Get user credit summary
     */
    public static function getCreditSummary(User $user): array
    {
        $userCredit = $user->credit;
        
        if (!$userCredit) {
            return [
                'total_credits' => 0,
                'used_credits' => 0,
                'available_credits' => 0,
                'usage_percentage' => 0,
            ];
        }

        $totalCredits = $userCredit->credits;
        $usedCredits = $userCredit->used_credits;
        $availableCredits = max(0, $totalCredits - $usedCredits);
        $usagePercentage = $totalCredits > 0 ? round(($usedCredits / $totalCredits) * 100, 1) : 0;

        return [
            'total_credits' => $totalCredits,
            'used_credits' => $usedCredits,
            'available_credits' => $availableCredits,
            'usage_percentage' => $usagePercentage,
        ];
    }
}
