<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\ScoringAttempt;
use App\Models\UserCredit;
use Illuminate\Support\Facades\Storage;

class AdminController extends Controller
{
    public function index()
    {
        $totalUsers = User::count();
        $totalAttempts = ScoringAttempt::count();
        $completedAttempts = ScoringAttempt::where('status', 'completed')->count();
        $totalCreditsUsed = UserCredit::sum('used_credits');

        $recentUsers = User::latest()->limit(5)->get();
        $recentAttempts = ScoringAttempt::with(['user', 'essayQuestion'])
            ->latest()
            ->limit(10)
            ->get();

        return view('admin.index', compact(
            'totalUsers',
            'totalAttempts',
            'completedAttempts',
            'totalCreditsUsed',
            'recentUsers',
            'recentAttempts'
        ));
    }

    /**
     * Show email subscribers
     */
    public function emailSubscribers()
    {
        $subscribers = $this->getEmailSubscribers();

        return view('admin.email-subscribers', compact('subscribers'));
    }

    /**
     * Get email subscribers from file
     */
    private function getEmailSubscribers()
    {
        try {
            $filePath = 'emails/speaking_ai_subscribers.txt';

            if (!Storage::exists($filePath)) {
                return collect([]);
            }

            $content = Storage::get($filePath);
            $lines = explode("\n", trim($content));

            $subscribers = collect();
            foreach ($lines as $line) {
                if (!empty($line)) {
                    $decoded = json_decode($line, true);
                    if ($decoded) {
                        $subscribers->push($decoded);
                    }
                }
            }

            // Sort by timestamp descending (newest first)
            return $subscribers->sortByDesc('timestamp');

        } catch (\Exception $e) {
            \Log::error('Failed to read email subscriptions: ' . $e->getMessage());
            return collect([]);
        }
    }

    /**
     * Export email subscribers
     */
    public function exportEmailSubscribers()
    {
        $subscribers = $this->getEmailSubscribers();

        $filename = 'speaking_ai_subscribers_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($subscribers) {
            $file = fopen('php://output', 'w');

            // Add BOM for UTF-8
            fwrite($file, "\xEF\xBB\xBF");

            // CSV headers
            fputcsv($file, ['Email', 'Ngày đăng ký', 'IP Address', 'User Agent']);

            foreach ($subscribers as $subscriber) {
                fputcsv($file, [
                    $subscriber['email'] ?? '',
                    $subscriber['timestamp'] ?? '',
                    $subscriber['ip'] ?? '',
                    $subscriber['user_agent'] ?? ''
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
