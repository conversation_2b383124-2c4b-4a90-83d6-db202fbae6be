<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;

class SettingsController extends Controller
{
    /**
     * Display the settings page
     */
    public function index()
    {
        $settings = [
            'overall_band_score' => config('scoring.adjustments.overall_band_score'),
            'task_achievement' => config('scoring.adjustments.task_achievement'),
            'coherence_cohesion' => config('scoring.adjustments.coherence_cohesion'),
            'lexical_resource' => config('scoring.adjustments.lexical_resource'),
            'grammar_accuracy' => config('scoring.adjustments.grammar_accuracy'),
            'enable_rounding' => config('scoring.enable_rounding'),
            'minimum_score' => config('scoring.minimum_score'),
            'maximum_score' => config('scoring.maximum_score'),
            'show_original_scores' => config('scoring.display.show_original_scores'),
            'show_adjustment_info' => config('scoring.display.show_adjustment_info'),
        ];

        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Update score adjustment settings
     */
    public function updateScoreAdjustments(Request $request)
    {
        $request->validate([
            'overall_band_score' => 'required|numeric|min:0|max:9',
            'task_achievement' => 'required|numeric|min:0|max:9',
            'coherence_cohesion' => 'required|numeric|min:0|max:9',
            'lexical_resource' => 'required|numeric|min:0|max:9',
            'grammar_accuracy' => 'required|numeric|min:0|max:9',
            'enable_rounding' => 'boolean',
            'minimum_score' => 'required|numeric|min:0|max:9',
            'maximum_score' => 'required|numeric|min:0|max:9',
            'show_original_scores' => 'boolean',
            'show_adjustment_info' => 'boolean',
        ]);

        try {
            // Update .env file with new values
            $this->updateEnvFile([
                'SCORE_ADJUSTMENT_OVERALL' => $request->overall_band_score,
                'SCORE_ADJUSTMENT_TASK_ACHIEVEMENT' => $request->task_achievement,
                'SCORE_ADJUSTMENT_COHERENCE_COHESION' => $request->coherence_cohesion,
                'SCORE_ADJUSTMENT_LEXICAL_RESOURCE' => $request->lexical_resource,
                'SCORE_ADJUSTMENT_GRAMMAR_ACCURACY' => $request->grammar_accuracy,
                'SCORE_ENABLE_ROUNDING' => $request->has('enable_rounding') ? 'true' : 'false',
                'SCORE_MINIMUM_THRESHOLD' => $request->minimum_score,
                'SCORE_MAXIMUM_THRESHOLD' => $request->maximum_score,
                'SCORE_SHOW_ORIGINAL' => $request->has('show_original_scores') ? 'true' : 'false',
                'SCORE_SHOW_ADJUSTMENT_INFO' => $request->has('show_adjustment_info') ? 'true' : 'false',
            ]);

            // Clear config cache
            Artisan::call('config:clear');

            return redirect()->back()->with('success', 'Cài đặt điều chỉnh điểm số đã được cập nhật thành công!');

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Có lỗi xảy ra khi cập nhật cài đặt: ' . $e->getMessage());
        }
    }

    /**
     * Apply uniform adjustment to all criteria
     */
    public function applyUniformAdjustment(Request $request)
    {
        $request->validate([
            'uniform_adjustment' => 'required|numeric|min:0|max:9',
        ]);

        try {
            $adjustment = $request->uniform_adjustment;

            // Apply the same adjustment to all criteria
            $this->updateEnvFile([
                'SCORE_ADJUSTMENT_OVERALL' => $adjustment,
                'SCORE_ADJUSTMENT_TASK_ACHIEVEMENT' => $adjustment,
                'SCORE_ADJUSTMENT_COHERENCE_COHESION' => $adjustment,
                'SCORE_ADJUSTMENT_LEXICAL_RESOURCE' => $adjustment,
                'SCORE_ADJUSTMENT_GRAMMAR_ACCURACY' => $adjustment,
            ]);

            // Clear config cache
            Artisan::call('config:clear');

            return redirect()->back()->with('success', "Đã áp dụng điều chỉnh {$adjustment} điểm cho tất cả các tiêu chí!");

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Có lỗi xảy ra khi áp dụng điều chỉnh đồng loạt: ' . $e->getMessage());
        }
    }

    /**
     * Reset all adjustments to default values
     */
    public function resetToDefaults()
    {
        try {
            $this->updateEnvFile([
                'SCORE_ADJUSTMENT_OVERALL' => '0.5',
                'SCORE_ADJUSTMENT_TASK_ACHIEVEMENT' => '0.5',
                'SCORE_ADJUSTMENT_COHERENCE_COHESION' => '0.5',
                'SCORE_ADJUSTMENT_LEXICAL_RESOURCE' => '0.5',
                'SCORE_ADJUSTMENT_GRAMMAR_ACCURACY' => '0.5',
                'SCORE_ENABLE_ROUNDING' => 'true',
                'SCORE_MINIMUM_THRESHOLD' => '0.0',
                'SCORE_MAXIMUM_THRESHOLD' => '9.0',
                'SCORE_SHOW_ORIGINAL' => 'false',
                'SCORE_SHOW_ADJUSTMENT_INFO' => 'false',
            ]);

            // Clear config cache
            Artisan::call('config:clear');

            return redirect()->back()->with('success', 'Đã khôi phục cài đặt về giá trị mặc định!');

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Có lỗi xảy ra khi khôi phục cài đặt: ' . $e->getMessage());
        }
    }

    /**
     * Update .env file with new values
     */
    private function updateEnvFile(array $data)
    {
        $envFile = base_path('.env');
        $envContent = file_get_contents($envFile);

        foreach ($data as $key => $value) {
            $pattern = "/^{$key}=.*/m";
            $replacement = "{$key}={$value}";

            if (preg_match($pattern, $envContent)) {
                $envContent = preg_replace($pattern, $replacement, $envContent);
            } else {
                $envContent .= "\n{$replacement}";
            }
        }

        file_put_contents($envFile, $envContent);
    }
}
