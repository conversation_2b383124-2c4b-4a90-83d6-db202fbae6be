<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ScoringAttempt;
use App\Models\User;
use Illuminate\Http\Request;

class ScoringAttemptController extends Controller
{
    /**
     * Display a listing of all scoring attempts
     */
    public function index(Request $request)
    {
        $query = ScoringAttempt::with(['user', 'essayQuestion'])
            ->latest();

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by task type
        if ($request->filled('task_type')) {
            $query->where('task_type', $request->task_type);
        }

        // Filter by user
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Search by user name or email
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $attempts = $query->paginate(20);

        // Get filter options
        $users = User::orderBy('name')->get(['id', 'name', 'email']);
        $statuses = ['pending', 'completed', 'failed'];
        $taskTypes = ['task1_academic', 'task1_general', 'task2'];

        // Statistics
        $stats = [
            'total' => ScoringAttempt::count(),
            'completed' => ScoringAttempt::where('status', 'completed')->count(),
            'pending' => ScoringAttempt::where('status', 'pending')->count(),
            'failed' => ScoringAttempt::where('status', 'failed')->count(),
            'today' => ScoringAttempt::whereDate('created_at', today())->count(),
            'this_week' => ScoringAttempt::whereBetween('created_at', [
                now()->startOfWeek(),
                now()->endOfWeek()
            ])->count(),
            'this_month' => ScoringAttempt::whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count(),
        ];

        return view('admin.scoring-attempts.index', compact(
            'attempts',
            'users',
            'statuses',
            'taskTypes',
            'stats'
        ));
    }

    /**
     * Display the specified scoring attempt
     */
    public function show($id)
    {
        $attempt = ScoringAttempt::findOrFail($id);
        $attempt->load(['user', 'essayQuestion']);

        return view('admin.scoring-attempts.show', compact('attempt'));
    }

    /**
     * Delete the specified scoring attempt
     */
    public function destroy($id)
    {
        try {
            $attempt = ScoringAttempt::findOrFail($id);
            $attempt->delete();

            return redirect()->route('admin.scoring-attempts.index')
                ->with('success', 'Bài thi đã được xóa thành công.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Có lỗi xảy ra khi xóa bài thi: ' . $e->getMessage());
        }
    }

    /**
     * Bulk delete scoring attempts
     */
    public function bulkDelete(Request $request)
    {
        $request->validate([
            'attempt_ids' => 'required|array',
            'attempt_ids.*' => 'exists:scoring_attempts,id'
        ]);

        try {
            ScoringAttempt::whereIn('id', $request->attempt_ids)->delete();

            $count = count($request->attempt_ids);
            return redirect()->back()
                ->with('success', "Đã xóa {$count} bài thi thành công.");
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Có lỗi xảy ra khi xóa bài thi: ' . $e->getMessage());
        }
    }

    /**
     * Export scoring attempts to CSV
     */
    public function export(Request $request)
    {
        $query = ScoringAttempt::with(['user', 'essayQuestion']);

        // Apply same filters as index
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('task_type')) {
            $query->where('task_type', $request->task_type);
        }

        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $attempts = $query->get();

        $filename = 'scoring_attempts_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($attempts) {
            $file = fopen('php://output', 'w');

            // Add BOM for UTF-8
            fwrite($file, "\xEF\xBB\xBF");

            // CSV headers
            fputcsv($file, [
                'ID',
                'Người dùng',
                'Email',
                'Loại bài thi',
                'Câu hỏi',
                'Điểm tổng',
                'Task Achievement',
                'Coherence & Cohesion',
                'Lexical Resource',
                'Grammar Accuracy',
                'Số từ',
                'Trạng thái',
                'Ngày tạo',
                'Ngày hoàn thành'
            ]);

            foreach ($attempts as $attempt) {
                fputcsv($file, [
                    $attempt->id,
                    $attempt->user->name,
                    $attempt->user->email,
                    ucfirst(str_replace('_', ' ', $attempt->task_type)),
                    $attempt->essayQuestion ? $attempt->essayQuestion->title : 'Custom Question',
                    $attempt->overall_band_score ?? 'N/A',
                    $attempt->task_achievement ?? 'N/A',
                    $attempt->coherence_cohesion ?? 'N/A',
                    $attempt->lexical_resource ?? 'N/A',
                    $attempt->grammar_accuracy ?? 'N/A',
                    $attempt->word_count ?? 0,
                    ucfirst($attempt->status),
                    $attempt->created_at->format('d/m/Y H:i'),
                    $attempt->status === 'completed' ? $attempt->updated_at->format('d/m/Y H:i') : 'N/A'
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
