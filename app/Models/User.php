<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use App\Traits\VietnamTimezone;
use App\Models\UserCredit;
use App\Models\ScoringAttempt;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, VietnamTimezone;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'phone',
        'password',
        'role',
        'is_active',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Check if user is admin
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    /**
     * Get user's credit information
     */
    public function credit()
    {
        return $this->hasOne(UserCredit::class);
    }

    /**
     * Get user's IELTS attempts
     */
    public function ieltsAttempts()
    {
        return $this->hasMany(IeltsAttempt::class);
    }

    /**
     * Get user's scoring attempts
     */
    public function scoringAttempts()
    {
        return $this->hasMany(ScoringAttempt::class);
    }

    /**
     * Get available credits
     */
    public function getAvailableCredits(): int
    {
        $credit = $this->credit;
        if (!$credit) {
            return 0;
        }
        return max(0, $credit->credits - $credit->used_credits);
    }

    /**
     * Use credits
     */
    public function useCredits(int $amount = 1): bool
    {
        $credit = $this->credit;
        if (!$credit) {
            return false;
        }

        if ($this->getAvailableCredits() >= $amount) {
            $credit->increment('used_credits', $amount);
            return true;
        }

        return false;
    }
}
