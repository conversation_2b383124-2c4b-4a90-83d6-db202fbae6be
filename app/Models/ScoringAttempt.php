<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Services\HashidService;
use App\Traits\VietnamTimezone;

class ScoringAttempt extends Model
{
    use VietnamTimezone;
    protected $fillable = [
        'user_id',
        'essay_question_id',
        'custom_question',
        'essay_content',
        'task_type',
        'time_limit',
        'overall_band_score',
        'task_achievement',
        'coherence_cohesion',
        'lexical_resource',
        'grammar_accuracy',
        'detailed_feedback',
        'highlighted_corrections',
        'criteria_analysis',
        'statistics',
        'word_count',
        'essay_length',
        'status',
        'error_message',
        'rescored_at',
    ];

    protected $casts = [
        'detailed_feedback' => 'array',
        'highlighted_corrections' => 'array',
        'criteria_analysis' => 'array',
        'statistics' => 'array',
        'overall_band_score' => 'decimal:1',
        'task_achievement' => 'decimal:1',
        'coherence_cohesion' => 'decimal:1',
        'lexical_resource' => 'decimal:1',
        'grammar_accuracy' => 'decimal:1',
        'rescored_at' => 'datetime',
    ];

    /**
     * Get the user that owns the scoring attempt
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the essay question
     */
    public function essayQuestion(): BelongsTo
    {
        return $this->belongsTo(EssayQuestion::class);
    }

    /**
     * Check if scoring is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if scoring failed
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Get overall band score with configurable adjustment
     */
    public function getOverallBandScoreAttribute($value)
    {
        if ($value === null) {
            return null;
        }

        return $this->adjustScore($value, 'overall_band_score');
    }

    /**
     * Get task achievement score with configurable adjustment
     */
    public function getTaskAchievementAttribute($value)
    {
        if ($value === null) {
            return null;
        }

        return $this->adjustScore($value, 'task_achievement');
    }

    /**
     * Get coherence cohesion score with configurable adjustment
     */
    public function getCoherenceCohesionAttribute($value)
    {
        if ($value === null) {
            return null;
        }

        return $this->adjustScore($value, 'coherence_cohesion');
    }

    /**
     * Get lexical resource score with configurable adjustment
     */
    public function getLexicalResourceAttribute($value)
    {
        if ($value === null) {
            return null;
        }

        return $this->adjustScore($value, 'lexical_resource');
    }

    /**
     * Get grammar accuracy score with configurable adjustment
     */
    public function getGrammarAccuracyAttribute($value)
    {
        if ($value === null) {
            return null;
        }

        return $this->adjustScore($value, 'grammar_accuracy');
    }

    /**
     * Apply score adjustment based on configuration
     */
    private function adjustScore($value, $scoreType)
    {
        $adjustment = config("scoring.adjustments.{$scoreType}", 0);
        $adjustedScore = (float) $value - $adjustment;

        // Apply minimum and maximum thresholds
        $minScore = config('scoring.minimum_score', 0.0);
        $maxScore = config('scoring.maximum_score', 9.0);
        $adjustedScore = max($minScore, min($maxScore, $adjustedScore));

        // Apply IELTS-standard rounding if enabled
        if (config('scoring.enable_rounding', true)) {
            $adjustedScore = round($adjustedScore * 2) / 2;
        }

        return $adjustedScore;
    }

    /**
     * Get original overall band score (without adjustment)
     */
    public function getOriginalOverallBandScore()
    {
        return $this->attributes['overall_band_score'] ?? null;
    }

    /**
     * Get original task achievement score (without adjustment)
     */
    public function getOriginalTaskAchievement()
    {
        return $this->attributes['task_achievement'] ?? null;
    }

    /**
     * Get original coherence cohesion score (without adjustment)
     */
    public function getOriginalCoherenceCohesion()
    {
        return $this->attributes['coherence_cohesion'] ?? null;
    }

    /**
     * Get original lexical resource score (without adjustment)
     */
    public function getOriginalLexicalResource()
    {
        return $this->attributes['lexical_resource'] ?? null;
    }

    /**
     * Get original grammar accuracy score (without adjustment)
     */
    public function getOriginalGrammarAccuracy()
    {
        return $this->attributes['grammar_accuracy'] ?? null;
    }

    /**
     * Get all original scores (without adjustments)
     */
    public function getOriginalScores()
    {
        return [
            'overall_band_score' => $this->getOriginalOverallBandScore(),
            'task_achievement' => $this->getOriginalTaskAchievement(),
            'coherence_cohesion' => $this->getOriginalCoherenceCohesion(),
            'lexical_resource' => $this->getOriginalLexicalResource(),
            'grammar_accuracy' => $this->getOriginalGrammarAccuracy(),
        ];
    }

    /**
     * Get band score description
     */
    public function getBandDescription(): string
    {
        if (!$this->overall_band_score) {
            return 'Not scored yet';
        }

        $score = (float) $this->overall_band_score;

        if ($score >= 8.5) {
            return 'Expert User';
        } elseif ($score >= 7.5) {
            return 'Very Good User';
        } elseif ($score >= 6.5) {
            return 'Competent User';
        } elseif ($score >= 5.5) {
            return 'Modest User';
        } elseif ($score >= 4.5) {
            return 'Limited User';
        } elseif ($score >= 3.5) {
            return 'Extremely Limited User';
        } else {
            return 'Intermittent User';
        }
    }

    /**
     * Calculate CEFR Level based on overall band score
     */
    public function getCefrLevel(): string
    {
        if (!$this->overall_band_score) {
            return 'A1';
        }

        $score = (float) $this->overall_band_score;

        // Map IELTS band scores to CEFR levels
        if ($score >= 8.5) return 'C2';
        if ($score >= 7.0) return 'C1';
        if ($score >= 5.5) return 'B2';
        if ($score >= 4.0) return 'B1';
        if ($score >= 3.0) return 'A2';

        return 'A1';
    }

    /**
     * Get hashid for this attempt
     */
    public function getHashid(): string
    {
        return HashidService::encodeAttempt($this->id);
    }

    /**
     * Generate share URL for this attempt (public, no auth required)
     */
    public function getShareUrl(): string
    {
        return route('scoring.share', ['attempt' => $this->getHashid()]);
    }

    /**
     * Get route key name for model binding
     */
    public function getRouteKeyName(): string
    {
        return 'hashid';
    }

    /**
     * Retrieve the model for a bound value
     */
    public function resolveRouteBinding($value, $field = null)
    {
        // If it's numeric, it's the old ID format
        if (is_numeric($value)) {
            return $this->where('id', $value)->first();
        }

        // If it's a hashid, decode it
        $id = HashidService::decodeAttempt($value);

        if ($id) {
            return $this->where('id', $id)->first();
        }

        return null;
    }

    /**
     * Check if this attempt can be shared publicly
     */
    public function canBeShared(): bool
    {
        return $this->isCompleted() && $this->overall_band_score !== null;
    }

    /**
     * Get the question text (either from essay_question or custom_question)
     */
    public function getQuestionText(): ?string
    {
        if ($this->essayQuestion) {
            return $this->essayQuestion->question;
        }

        return $this->custom_question;
    }
}
