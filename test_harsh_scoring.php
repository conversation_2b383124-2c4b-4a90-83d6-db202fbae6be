<?php

echo "=== TESTING ULTRA-HARSH IELTS SCORING ===\n\n";

// Test essay with very poor quality (should get 2.0-3.0)
$weakEssay = "
Now people work more and no much time for family. This happen many country. People work too much. Sometime morning to night. They not happy but still work.

The cause is money. People want money so they work. Company make people work long hours. If no work, no money, no food. Some people want buy car or phone, so need work long.

Solution is rest. People need holiday. Government can tell company give free time. Family is important. Sometime go home early to eat with children. Also maybe not work Sunday.

In conclusion, work too much is not good. People must stop work so much and play with family.
";

$question = "In many countries, people are working longer hours and have less time for family and leisure activities. What are the causes of this trend? What solutions can you suggest?";

echo "Question: " . $question . "\n\n";
echo "Essay Content:\n" . trim($weakEssay) . "\n\n";
echo "Word Count: " . str_word_count($weakEssay) . " words\n\n";

// Manual error detection for this very weak essay
$errors = [
    // Grammar errors
    ["no much time" => "not much time", "type" => "grammar", "severity" => "high"],
    ["This happen" => "This happens", "type" => "grammar", "severity" => "high"],
    ["Sometime morning" => "Sometimes from morning", "type" => "grammar", "severity" => "high"],
    ["They not happy" => "They are not happy", "type" => "grammar", "severity" => "high"],
    ["Company make" => "Companies make", "type" => "grammar", "severity" => "high"],
    ["If no work" => "If there is no work", "type" => "grammar", "severity" => "high"],
    ["want buy" => "want to buy", "type" => "grammar", "severity" => "high"],
    ["need work long" => "need to work long", "type" => "grammar", "severity" => "high"],
    ["People need holiday" => "People need holidays", "type" => "grammar", "severity" => "medium"],
    ["Government can tell company" => "The government can tell companies", "type" => "grammar", "severity" => "high"],
    ["give free time" => "to give free time", "type" => "grammar", "severity" => "medium"],
    ["Sometime go home" => "Sometimes go home", "type" => "grammar", "severity" => "medium"],
    ["maybe not work Sunday" => "maybe not work on Sunday", "type" => "grammar", "severity" => "medium"],
    ["work too much is not good" => "working too much is not good", "type" => "grammar", "severity" => "high"],
    ["must stop work" => "must stop working", "type" => "grammar", "severity" => "high"],
    
    // Missing articles
    ["People want money" => "People want THE money", "type" => "grammar", "severity" => "medium"],
    ["Family is important" => "THE family is important", "type" => "grammar", "severity" => "medium"],
    
    // Vocabulary issues
    ["work" => "repetitive use", "type" => "vocabulary", "severity" => "medium"],
    ["people" => "repetitive use", "type" => "vocabulary", "severity" => "medium"],
    
    // Sentence structure
    ["Incomplete sentences" => "Multiple fragments", "type" => "structure", "severity" => "high"],
    ["Run-on sentences" => "Poor connections", "type" => "structure", "severity" => "high"]
];

echo "=== ERRORS DETECTED ===\n";
echo "Total errors found: " . count($errors) . "\n\n";

$highSeverity = array_filter($errors, function($e) { return $e['severity'] === 'high'; });
$mediumSeverity = array_filter($errors, function($e) { return $e['severity'] === 'medium'; });

echo "High severity errors: " . count($highSeverity) . "\n";
echo "Medium severity errors: " . count($mediumSeverity) . "\n\n";

foreach ($errors as $i => $error) {
    if ($i < 10) { // Show first 10 errors
        $original = array_keys($error)[0];
        $corrected = $error[$original];
        echo ($i + 1) . ". \"$original\" → \"$corrected\"\n";
        echo "   Type: " . $error['type'] . " | Severity: " . $error['severity'] . "\n\n";
    }
}

// Scoring analysis
$totalErrors = count($errors);
$wordCount = str_word_count($weakEssay);
$errorDensity = ($totalErrors / $wordCount) * 100;

echo "=== HARSH SCORING ANALYSIS ===\n";
echo "Word Count: " . $wordCount . " words (below minimum 250)\n";
echo "Total Errors: " . $totalErrors . "\n";
echo "Error Density: " . round($errorDensity, 2) . " errors per 100 words\n\n";

// Apply ultra-harsh scoring rules
$suggestedScores = [
    'task_achievement' => 3.0, // Poor task response, minimal development
    'coherence_cohesion' => 2.5, // Very poor organization, no clear progression
    'lexical_resource' => 2.5, // Very limited vocabulary, repetitive
    'grammar_accuracy' => 2.0  // Many errors impeding communication
];

$overallScore = array_sum($suggestedScores) / 4;

echo "=== SUGGESTED BAND SCORES ===\n";
foreach ($suggestedScores as $criterion => $score) {
    echo ucfirst(str_replace('_', ' ', $criterion)) . ": " . $score . "\n";
}
echo "Overall Band Score: " . round($overallScore, 1) . "\n\n";

echo "=== VALIDATION CHECKS ===\n";
echo "✓ High error count (20+): " . ($totalErrors >= 20 ? "PASS" : "FAIL") . "\n";
echo "✓ Very high error density (>15%): " . ($errorDensity > 15 ? "PASS" : "FAIL") . "\n";
echo "✓ Score reflects quality (2.0-3.5): " . ($overallScore <= 3.5 ? "PASS" : "FAIL") . "\n";
echo "✓ Under word count penalty applied: " . ($wordCount < 250 ? "PASS" : "N/A") . "\n";

echo "\n=== EXPECTED IMPROVEMENTS ===\n";
echo "- This essay should score 2.5-3.0 (NOT 4.0+)\n";
echo "- System should find 20+ errors\n";
echo "- Grammar score should be 2.0-3.0\n";
echo "- Validation should enforce harsh penalties\n";

echo "\n=== ULTRA-HARSH PROMPT FEATURES ===\n";
echo "1. ✓ 'EXTREMELY HARSH and UNFORGIVING'\n";
echo "2. ✓ 'RARELY give scores above 6.0'\n";
echo "3. ✓ 'Most students 2.0-5.5'\n";
echo "4. ✓ 'Basic grammar wrong → Max 4.0'\n";
echo "5. ✓ 'Very limited vocabulary → Max 3.5'\n";
echo "6. ✓ 'Incomplete sentences → Max 3.0'\n";
echo "7. ✓ 'When in doubt, REDUCE by 1.0-1.5'\n";

echo "\n=== TEST COMPLETED ===\n";
