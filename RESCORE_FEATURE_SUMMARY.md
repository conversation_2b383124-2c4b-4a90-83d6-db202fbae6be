# Tính năng "Chấm lại điểm" - Rescore Feature

## 🎯 Tổng quan

Đã thêm thành công tính năng **"Chấm lại điểm"** cho phép người dùng chấm lại bài essay đã hoàn thành với điều kiện **vẫn trừ 1 credit**.

## 🔧 C<PERSON>c thay đổi đã thực hiện

### 1. **Frontend (View)**
- **File**: `resources/views/scoring/show.blade.php`
- **Thêm nút**: "Chấm lại điểm (Trừ 1 credit)" với icon redo
- **Styling**: Nút màu warning với hover effects
- **JavaScript**: Function `rescoreEssay()` với confirmation dialog

### 2. **Backend (Controller)**
- **File**: `app/Http/Controllers/ScoringController.php`
- **Method mới**: `rescore(Request $request, $hashid)`
- **Validation**: <PERSON><PERSON><PERSON> tra quyền sở hữu, credits, trạng thái bài thi
- **Logic**: Trừ credit → Chấm lại → Cập nhật kết quả

### 3. **Routes**
- **File**: `routes/web.php`
- **Route mới**: `POST /scoring/rescore/{attempt}`
- **Name**: `scoring.rescore`

### 4. **Database**
- **Migration**: `2025_07_19_013912_add_rescored_at_to_scoring_attempts_table.php`
- **Cột mới**: `rescored_at` (timestamp, nullable)
- **Model**: Thêm vào fillable và casts

## 📋 Quy trình hoạt động

### 1. **Hiển thị nút**
```php
@if($attempt->isCompleted())
<button onclick="rescoreEssay()" class="btn btn-warning" id="rescoreBtn">
    <i class="fas fa-redo"></i> Chấm lại điểm (Trừ 1 credit)
</button>
@endif
```

### 2. **Xác nhận từ người dùng**
```javascript
if (!confirm('Bạn có chắc chắn muốn chấm lại điểm? Điều này sẽ trừ 1 credit từ tài khoản của bạn.')) {
    return;
}
```

### 3. **Validation backend**
- Kiểm tra bài thi tồn tại
- Kiểm tra quyền sở hữu
- Kiểm tra trạng thái completed
- Kiểm tra đủ credits (≥ 1)

### 4. **Xử lý chấm lại**
```php
// Trừ credit
$user->useCredits(1);

// Chấm lại với prompt mới (ultra-harsh)
$result = $this->scorer->scoreEssayComprehensive(
    $attempt->essay_content,
    $attempt->task_type,
    $essayQuestion,
    $attempt->time_limit ?? 40
);

// Cập nhật kết quả
$attempt->update([
    'overall_band_score' => $result['overall_band_score'],
    // ... các trường khác
    'rescored_at' => now(),
]);
```

## 🔒 Bảo mật & Validation

### 1. **Authentication**
- Chỉ user đã login mới có thể rescore
- Middleware `auth` bảo vệ route

### 2. **Authorization**
- Chỉ chủ sở hữu bài thi mới được rescore
- Kiểm tra `$attempt->user_id === $user->id`

### 3. **Business Logic**
- Chỉ bài thi đã completed mới được rescore
- Phải có đủ credits (≥ 1)
- Trừ credit trước khi chấm

### 4. **Error Handling**
- Try-catch với DB transaction
- Rollback nếu có lỗi
- Log chi tiết cho debugging

## 💰 Credit Management

### **Trước rescore:**
```php
if ($user->getAvailableCredits() < 1) {
    return response()->json([
        'success' => false,
        'message' => 'Bạn không có đủ lượt chấm thi.'
    ], 400);
}
```

### **Sử dụng credit:**
```php
$user->useCredits(1);
```

### **Rollback nếu lỗi:**
```php
DB::rollback(); // Credit sẽ được hoàn lại
```

## 📊 Logging & Monitoring

### **Log rescoring:**
```php
\Log::info('Starting essay rescoring', [
    'user_id' => $user->id,
    'attempt_id' => $attempt->id,
    'task_type' => $attempt->task_type,
    'word_count' => $attempt->word_count
]);

\Log::info('Essay rescoring completed', [
    'user_id' => $user->id,
    'attempt_id' => $attempt->id,
    'old_score' => $attempt->overall_band_score,
    'new_score' => $result['overall_band_score']
]);
```

## 🎨 UI/UX Features

### **Loading State:**
```javascript
rescoreBtn.disabled = true;
rescoreBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang chấm lại...';
```

### **Success Feedback:**
```javascript
alert('Chấm điểm thành công! Trang sẽ được tải lại để hiển thị kết quả mới.');
window.location.reload();
```

### **Error Handling:**
```javascript
alert('Lỗi: ' + (data.message || 'Không thể chấm lại điểm. Vui lòng thử lại sau.'));
```

## 🔄 Kết quả mong đợi

### **Với prompt ultra-harsh mới:**
1. **Bài yếu** (nhiều lỗi) sẽ được điểm thấp hơn
2. **Validation tự động** điều chỉnh điểm không hợp lý
3. **Phát hiện lỗi** toàn diện hơn (10-25 lỗi)
4. **Chấm điểm** chính xác phản ánh chất lượng

### **Ví dụ:**
- **Lần 1**: Band 4.0 (prompt cũ)
- **Rescore**: Band 2.5-3.0 (prompt ultra-harsh)

## ✅ Checklist triển khai

- [x] Thêm nút rescore vào view
- [x] Tạo JavaScript function với confirmation
- [x] Thêm route POST cho rescore
- [x] Implement controller method với validation
- [x] Tạo migration cho rescored_at
- [x] Cập nhật model fillable và casts
- [x] Thêm error handling và logging
- [x] Test với ultra-harsh prompt

## 🚀 Cách sử dụng

1. **Người dùng** vào trang kết quả bài thi đã completed
2. **Nhấn nút** "Chấm lại điểm (Trừ 1 credit)"
3. **Xác nhận** trong dialog popup
4. **Hệ thống** trừ 1 credit và chấm lại với prompt mới
5. **Trang reload** hiển thị kết quả mới

## 📈 Lợi ích

1. **Cho người dùng**: Có cơ hội được chấm với tiêu chuẩn mới
2. **Cho hệ thống**: Tăng revenue từ việc sử dụng credits
3. **Cho chất lượng**: Áp dụng prompt ultra-harsh mới
4. **Cho trải nghiệm**: Tính năng linh hoạt và minh bạch

**Kết luận**: Tính năng rescore đã được triển khai hoàn chỉnh với đầy đủ validation, security và user experience tốt!
