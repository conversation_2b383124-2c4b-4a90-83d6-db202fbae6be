<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        $admin = \App\Models\User::create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('admin123'),
            'role' => 'admin',
            'is_active' => true,
        ]);

        // Create admin credit record
        \App\Models\UserCredit::create([
            'user_id' => $admin->id,
            'credits' => 1000,
            'used_credits' => 0,
            'notes' => 'Admin account with unlimited credits',
        ]);

        // Create demo user
        $user = \App\Models\User::create([
            'name' => 'Demo User',
            'email' => '<EMAIL>',
            'password' => bcrypt('user123'),
            'role' => 'user',
            'is_active' => true,
        ]);

        // Create user credit record
        \App\Models\UserCredit::create([
            'user_id' => $user->id,
            'credits' => 10,
            'used_credits' => 0,
            'notes' => 'Demo user with 10 free credits',
        ]);
    }
}
