<!DOCTYPE html>
<html>
<head>
    <title>Test Email Form</title>
    <meta name="csrf-token" content="test-token">
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer; }
        .alert { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-danger { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <h2>Test Email Subscription Form</h2>
    
    <form id="testForm">
        <div class="form-group">
            <label>Email:</label>
            <input type="email" name="email" required style="width: 300px; padding: 8px;">
        </div>
        <button type="submit" class="btn">Subscribe</button>
    </form>
    
    <div id="message"></div>

    <script>
        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const messageDiv = document.getElementById('message');
            
            fetch('http://127.0.0.1:8000/subscribe', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': 'test-token',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    messageDiv.innerHTML = `<div class="alert alert-success">${data.message}</div>`;
                    this.reset();
                } else {
                    messageDiv.innerHTML = `<div class="alert alert-danger">${data.message}</div>`;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                messageDiv.innerHTML = `<div class="alert alert-danger">Error occurred!</div>`;
            });
        });
    </script>
</body>
</html>
