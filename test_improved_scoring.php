<?php

// Bootstrap <PERSON>vel
require_once __DIR__ . '/vendor/autoload.php';

// Load <PERSON>vel app
$app = require_once __DIR__ . '/bootstrap/app.php';

// Use the IELTSScorer service
use App\Services\IELTSScorer;

// Test essay with multiple errors
$testEssay = "
Nowadays, many people believes that education is more important than ever before. In my opinion, I am agree with this statement because education help people to get better job and improve their life.

First of all, education give people more opportunities in their career. For example, people who have university degree usually earn more money than people without degree. This is because employers prefer to hire educated workers who have good knowledge and skills. Moreover, educated people can work in different fields and have more choices in their life.

Secondly, education help people to develop their personality and become better citizens. When people study, they learn not only academic subjects but also how to think critically and solve problems. This skills are very useful in daily life and help people to make good decisions.

In conclusion, I believe that education is extremely important in modern society. It give people better opportunities and help them to become successful in their life. Therefore, governments should invest more money in education system to ensure that all people have access to quality education.
";

$testQuestion = "Some people believe that education is more important now than it was in the past. To what extent do you agree or disagree with this statement?";

echo "=== TESTING IMPROVED IELTS SCORING SYSTEM ===\n\n";
echo "Essay Question: " . $testQuestion . "\n\n";
echo "Essay Content:\n" . $testEssay . "\n\n";
echo "Word Count: " . str_word_count($testEssay) . " words\n\n";

try {
    $scorer = new IELTSScorer();
    $result = $scorer->scoreEssayComprehensive($testEssay, 'task2', $testQuestion, 40);

    echo "=== SCORING RESULTS ===\n";
    echo "Overall Band Score: " . $result['overall_band_score'] . "\n\n";

    echo "Criteria Scores:\n";
    foreach ($result['criteria_scores'] as $criterion => $score) {
        echo "- " . ucfirst(str_replace('_', ' ', $criterion)) . ": " . $score . "\n";
    }

    echo "\nTotal Errors Found: " . ($result['statistics']['total_errors'] ?? 'N/A') . "\n";
    echo "Grammar Errors: " . ($result['statistics']['grammar_errors'] ?? 'N/A') . "\n";
    echo "Spelling Errors: " . ($result['statistics']['spelling_errors'] ?? 'N/A') . "\n";

    if (isset($result['validation_adjustments'])) {
        echo "\nValidation Adjustments Applied:\n";
        foreach ($result['validation_adjustments'] as $adjustment) {
            echo "- " . $adjustment . "\n";
        }
    }

    if (isset($result['validation_warnings'])) {
        echo "\nValidation Warnings:\n";
        foreach ($result['validation_warnings'] as $warning) {
            echo "- " . $warning . "\n";
        }
    }

    echo "\nHighlighted Corrections (first 5):\n";
    $corrections = array_slice($result['highlighted_corrections'] ?? [], 0, 5);
    foreach ($corrections as $i => $correction) {
        echo ($i + 1) . ". \"" . $correction['original_text'] . "\" → \"" . $correction['suggested_correction'] . "\"\n";
        echo "   Type: " . $correction['error_type'] . " | Severity: " . $correction['severity'] . "\n";
        echo "   Explanation: " . $correction['explanation'] . "\n\n";
    }

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "=== TEST COMPLETED ===\n";
