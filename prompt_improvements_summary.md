# Cải thiện Prompt IELTS Scoring System

## Vấn đề đã được xác định:

1. **Thiếu tính nhất quán**: Prompt cũ không có hướng dẫn cụ thể về cách phát hiện lỗi
2. **Chấm điểm quá cao**: Thiếu tiêu chuẩn nghiêm ngặt, nhiều bài có lỗi vẫn được điểm cao
3. **Phát hiện lỗi không ổn định**: <PERSON><PERSON> lúc tìm được nhiều lỗi, có lúc tìm được ít lỗi
4. **Thiếu validation**: Không có cơ chế kiểm tra tính hợp lý của kết quả

## Cải thiện đã thực hiện:

### 1. **Prompt chính được cải thiện**:
- Thêm hướng dẫn "ERROR DETECTION PROTOCOL" với 5 bước cụ thể
- Thêm "STRICT BAND SCORE GUIDELINES" với mô tả rõ ràng từng band
- Thêm "MINIMUM ERROR REQUIREMENTS" - bắt buộc tìm ít nhất 5-15 lỗi
- Thêm "SCORING PENALTIES" - quy định cụ thể cách trừ điểm
- Thêm "QUALITY CONTROL CHECKLIST" - yêu cầu AI tự kiểm tra

### 2. **Cải thiện Task Instructions**:
- Thêm hướng dẫn cụ thể cho từng loại task
- Thêm danh sách "COMMON ERRORS TO FIND" cho mỗi task type
- Quy định rõ ràng về penalties (ví dụ: thiếu từ = tối đa Band 6.0)

### 3. **Thêm Common Error Examples**:
- Ví dụ cụ thể về lỗi grammar, spelling, vocabulary
- Giúp AI có tham chiếu rõ ràng về các lỗi cần tìm

### 4. **Validation System mới**:
- Thêm method `validateScoringConsistency()` 
- Kiểm tra 5 quy tắc validation:
  - Minimum error requirement (ít nhất 5 lỗi)
  - Error density vs score consistency
  - Word count penalty
  - Criteria scores alignment
  - Grammar score vs grammar errors

### 5. **Cải thiện Response Format**:
- Yêu cầu cụ thể hơn về feedback
- Thêm trường "error_density" trong statistics
- Thêm validation warnings và adjustments

## Kết quả mong đợi:

1. **Tính nhất quán cao hơn**: Mỗi lần chấm sẽ tìm được số lượng lỗi tương tự
2. **Chấm điểm chính xác hơn**: Điểm số phản ánh đúng chất lượng bài viết
3. **Phát hiện lỗi toàn diện**: Ít nhất 5-15 lỗi được tìm thấy trong mỗi bài
4. **Validation tự động**: Hệ thống tự điều chỉnh nếu kết quả không hợp lý

## Cách test:

1. Chạy file `test_improved_scoring.php` để test với bài mẫu
2. So sánh kết quả trước và sau cải thiện
3. Kiểm tra xem có đủ lỗi được tìm thấy không
4. Xem validation có hoạt động không

## Monitoring:

- Theo dõi trường `validation_adjustments` để xem hệ thống có điều chỉnh gì không
- Kiểm tra `validation_warnings` nếu có cảnh báo
- Đảm bảo `total_errors` luôn >= 5 trong mọi bài chấm
