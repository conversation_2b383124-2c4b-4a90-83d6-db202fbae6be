# Hướng Dẫn Sử Dụng Hệ Thống Điều Chỉnh Điểm Số

## 🎯 Tổng Quan

Hệ thống điều chỉnh điểm số cho phép admin cấu hình mức giảm điểm cho tất cả các tiêu chí chấm IELTS một cách linh hoạt và đồng bộ.

## ✨ Tính Năng Chính

### 1. **Điều Chỉnh Đồng Loạt**
- <PERSON><PERSON> dụng cùng một mức giảm điểm cho tất cả các tiêu chí
- Ví dụ: Giảm 0.5 điểm cho tất cả (Overall, Task Achievement, Coherence & Cohesion, Lexical Resource, Grammar Accuracy)

### 2. **Điều Chỉnh Chi Tiết**
- Cấu hình riêng biệt cho từng tiêu chí
- Linh hoạt trong việc điều chỉnh độ khó

### 3. **Cài Đặt An Toàn**
- G<PERSON>ớ<PERSON> hạn điểm tối thiểu và tối đa
- <PERSON><PERSON><PERSON> tròn theo chuẩn IELTS (0.5)
- <PERSON><PERSON><PERSON>ng cho phép điểm âm

## 🚀 Cách Sử Dụng

### Truy Cập Giao Diện Admin
1. Đăng nhập với tài khoản admin
2. Vào menu **"Cài Đặt Điểm Số"**
3. URL: `/admin/settings`

### Điều Chỉnh Đồng Loạt (Khuyến Nghị)
1. Trong phần **"Điều Chỉnh Đồng Loạt"**
2. Nhập mức giảm điểm mong muốn (ví dụ: 0.5)
3. Nhấn **"Áp Dụng"**
4. Tất cả các tiêu chí sẽ được giảm cùng một mức

### Điều Chỉnh Chi Tiết
1. Trong phần **"Điều Chỉnh Chi Tiết Theo Tiêu Chí"**
2. Cấu hình riêng cho từng tiêu chí:
   - Overall Band Score
   - Task Achievement
   - Coherence & Cohesion
   - Lexical Resource
   - Grammar & Accuracy
3. Nhấn **"Lưu Cài Đặt"**

### Khôi Phục Mặc Định
- Nhấn **"Khôi Phục Mặc Định"** để reset về giá trị 0.5 cho tất cả

## ⚙️ Cấu Hình Nâng Cao

### File Cấu Hình: `config/scoring.php`
```php
'adjustments' => [
    'overall_band_score' => 0.5,
    'task_achievement' => 0.5,
    'coherence_cohesion' => 0.5,
    'lexical_resource' => 0.5,
    'grammar_accuracy' => 0.5,
],
```

### Biến Môi Trường (.env)
```env
SCORE_ADJUSTMENT_OVERALL=0.5
SCORE_ADJUSTMENT_TASK_ACHIEVEMENT=0.5
SCORE_ADJUSTMENT_COHERENCE_COHESION=0.5
SCORE_ADJUSTMENT_LEXICAL_RESOURCE=0.5
SCORE_ADJUSTMENT_GRAMMAR_ACCURACY=0.5
SCORE_ENABLE_ROUNDING=true
SCORE_MINIMUM_THRESHOLD=0.0
SCORE_MAXIMUM_THRESHOLD=9.0
```

## 🔧 Cách Hoạt Động

### Model Accessors
Hệ thống sử dụng Eloquent Accessors để tự động điều chỉnh điểm khi truy cập:

```php
// Điểm gốc từ database: 7.0
// Điều chỉnh: -0.5
// Điểm hiển thị: 6.5

$attempt->overall_band_score; // 6.5 (đã điều chỉnh)
$attempt->getOriginalOverallBandScore(); // 7.0 (điểm gốc)
```

### Quy Tắc Điều Chỉnh
1. **Giảm điểm**: Điểm gốc - Mức điều chỉnh
2. **Giới hạn tối thiểu**: Không cho phép điểm < 0.0
3. **Giới hạn tối đa**: Không cho phép điểm > 9.0
4. **Làm tròn**: Làm tròn đến 0.5 gần nhất (chuẩn IELTS)

## 📊 Ví Dụ Thực Tế

### Trước Khi Điều Chỉnh
- Overall: 7.0
- Task Achievement: 6.5
- Coherence & Cohesion: 7.5
- Lexical Resource: 6.0
- Grammar Accuracy: 7.0

### Sau Khi Điều Chỉnh (-0.5)
- Overall: 6.5
- Task Achievement: 6.0
- Coherence & Cohesion: 7.0
- Lexical Resource: 5.5
- Grammar Accuracy: 6.5

## 🛡️ Bảo Mật & An Toàn

- Chỉ admin mới có quyền truy cập
- Validation đầu vào nghiêm ngặt
- Backup tự động cài đặt cũ
- Khôi phục nhanh về mặc định

## 🔄 Cập Nhật & Bảo Trì

### Clear Cache Sau Khi Thay Đổi
```bash
php artisan config:clear
```

### Kiểm Tra Cấu Hình
```bash
php artisan tinker
>>> config('scoring.adjustments.overall_band_score')
```

## 📝 Lưu Ý Quan Trọng

1. **Thay đổi có hiệu lực ngay lập tức** cho tất cả kết quả hiển thị
2. **Điểm gốc trong database không bị thay đổi** - chỉ điều chỉnh khi hiển thị
3. **Có thể khôi phục** về điểm gốc bất cứ lúc nào
4. **Áp dụng đồng bộ** cho tất cả views và API responses

## 🆘 Hỗ Trợ

Nếu gặp vấn đề, vui lòng:
1. Kiểm tra file `.env` có đầy đủ cấu hình
2. Chạy `php artisan config:clear`
3. Kiểm tra logs trong `storage/logs/laravel.log`
